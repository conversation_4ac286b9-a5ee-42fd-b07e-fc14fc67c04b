"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/yup";
exports.ids = ["vendor-chunks/yup"];
exports.modules = {

/***/ "(ssr)/./node_modules/yup/index.esm.js":
/*!***************************************!*\
  !*** ./node_modules/yup/index.esm.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArraySchema: () => (/* binding */ ArraySchema),\n/* harmony export */   BooleanSchema: () => (/* binding */ BooleanSchema),\n/* harmony export */   DateSchema: () => (/* binding */ DateSchema),\n/* harmony export */   LazySchema: () => (/* binding */ Lazy),\n/* harmony export */   MixedSchema: () => (/* binding */ MixedSchema),\n/* harmony export */   NumberSchema: () => (/* binding */ NumberSchema),\n/* harmony export */   ObjectSchema: () => (/* binding */ ObjectSchema),\n/* harmony export */   Schema: () => (/* binding */ Schema),\n/* harmony export */   StringSchema: () => (/* binding */ StringSchema),\n/* harmony export */   TupleSchema: () => (/* binding */ TupleSchema),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   addMethod: () => (/* binding */ addMethod),\n/* harmony export */   array: () => (/* binding */ create$2),\n/* harmony export */   bool: () => (/* binding */ create$7),\n/* harmony export */   boolean: () => (/* binding */ create$7),\n/* harmony export */   date: () => (/* binding */ create$4),\n/* harmony export */   defaultLocale: () => (/* binding */ locale),\n/* harmony export */   getIn: () => (/* binding */ getIn),\n/* harmony export */   isSchema: () => (/* binding */ isSchema),\n/* harmony export */   lazy: () => (/* binding */ create),\n/* harmony export */   mixed: () => (/* binding */ create$8),\n/* harmony export */   number: () => (/* binding */ create$5),\n/* harmony export */   object: () => (/* binding */ create$3),\n/* harmony export */   printValue: () => (/* binding */ printValue),\n/* harmony export */   reach: () => (/* binding */ reach),\n/* harmony export */   ref: () => (/* binding */ create$9),\n/* harmony export */   setLocale: () => (/* binding */ setLocale),\n/* harmony export */   string: () => (/* binding */ create$6),\n/* harmony export */   tuple: () => (/* binding */ create$1)\n/* harmony export */ });\n/* harmony import */ var property_expr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-expr */ \"(ssr)/./node_modules/property-expr/index.js\");\n/* harmony import */ var property_expr__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(property_expr__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tiny_case__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-case */ \"(ssr)/./node_modules/tiny-case/index.js\");\n/* harmony import */ var tiny_case__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(tiny_case__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var toposort__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! toposort */ \"(ssr)/./node_modules/toposort/index.js\");\n/* harmony import */ var toposort__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(toposort__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst toString = Object.prototype.toString;\nconst errorToString = Error.prototype.toString;\nconst regExpToString = RegExp.prototype.toString;\nconst symbolToString = typeof Symbol !== 'undefined' ? Symbol.prototype.toString : () => '';\nconst SYMBOL_REGEXP = /^Symbol\\((.*)\\)(.*)$/;\nfunction printNumber(val) {\n  if (val != +val) return 'NaN';\n  const isNegativeZero = val === 0 && 1 / val < 0;\n  return isNegativeZero ? '-0' : '' + val;\n}\nfunction printSimpleValue(val, quoteStrings = false) {\n  if (val == null || val === true || val === false) return '' + val;\n  const typeOf = typeof val;\n  if (typeOf === 'number') return printNumber(val);\n  if (typeOf === 'string') return quoteStrings ? `\"${val}\"` : val;\n  if (typeOf === 'function') return '[Function ' + (val.name || 'anonymous') + ']';\n  if (typeOf === 'symbol') return symbolToString.call(val).replace(SYMBOL_REGEXP, 'Symbol($1)');\n  const tag = toString.call(val).slice(8, -1);\n  if (tag === 'Date') return isNaN(val.getTime()) ? '' + val : val.toISOString(val);\n  if (tag === 'Error' || val instanceof Error) return '[' + errorToString.call(val) + ']';\n  if (tag === 'RegExp') return regExpToString.call(val);\n  return null;\n}\nfunction printValue(value, quoteStrings) {\n  let result = printSimpleValue(value, quoteStrings);\n  if (result !== null) return result;\n  return JSON.stringify(value, function (key, value) {\n    let result = printSimpleValue(this[key], quoteStrings);\n    if (result !== null) return result;\n    return value;\n  }, 2);\n}\n\nfunction toArray(value) {\n  return value == null ? [] : [].concat(value);\n}\n\nlet _Symbol$toStringTag, _Symbol$hasInstance, _Symbol$toStringTag2;\nlet strReg = /\\$\\{\\s*(\\w+)\\s*\\}/g;\n_Symbol$toStringTag = Symbol.toStringTag;\nclass ValidationErrorNoStack {\n  constructor(errorOrErrors, value, field, type) {\n    this.name = void 0;\n    this.message = void 0;\n    this.value = void 0;\n    this.path = void 0;\n    this.type = void 0;\n    this.params = void 0;\n    this.errors = void 0;\n    this.inner = void 0;\n    this[_Symbol$toStringTag] = 'Error';\n    this.name = 'ValidationError';\n    this.value = value;\n    this.path = field;\n    this.type = type;\n    this.errors = [];\n    this.inner = [];\n    toArray(errorOrErrors).forEach(err => {\n      if (ValidationError.isError(err)) {\n        this.errors.push(...err.errors);\n        const innerErrors = err.inner.length ? err.inner : [err];\n        this.inner.push(...innerErrors);\n      } else {\n        this.errors.push(err);\n      }\n    });\n    this.message = this.errors.length > 1 ? `${this.errors.length} errors occurred` : this.errors[0];\n  }\n}\n_Symbol$hasInstance = Symbol.hasInstance;\n_Symbol$toStringTag2 = Symbol.toStringTag;\nclass ValidationError extends Error {\n  static formatError(message, params) {\n    // Attempt to make the path more friendly for error message interpolation.\n    const path = params.label || params.path || 'this';\n    // Store the original path under `originalPath` so it isn't lost to custom\n    // message functions; e.g., ones provided in `setLocale()` calls.\n    params = Object.assign({}, params, {\n      path,\n      originalPath: params.path\n    });\n    if (typeof message === 'string') return message.replace(strReg, (_, key) => printValue(params[key]));\n    if (typeof message === 'function') return message(params);\n    return message;\n  }\n  static isError(err) {\n    return err && err.name === 'ValidationError';\n  }\n  constructor(errorOrErrors, value, field, type, disableStack) {\n    const errorNoStack = new ValidationErrorNoStack(errorOrErrors, value, field, type);\n    if (disableStack) {\n      return errorNoStack;\n    }\n    super();\n    this.value = void 0;\n    this.path = void 0;\n    this.type = void 0;\n    this.params = void 0;\n    this.errors = [];\n    this.inner = [];\n    this[_Symbol$toStringTag2] = 'Error';\n    this.name = errorNoStack.name;\n    this.message = errorNoStack.message;\n    this.type = errorNoStack.type;\n    this.value = errorNoStack.value;\n    this.path = errorNoStack.path;\n    this.errors = errorNoStack.errors;\n    this.inner = errorNoStack.inner;\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ValidationError);\n    }\n  }\n  static [_Symbol$hasInstance](inst) {\n    return ValidationErrorNoStack[Symbol.hasInstance](inst) || super[Symbol.hasInstance](inst);\n  }\n}\n\nlet mixed = {\n  default: '${path} is invalid',\n  required: '${path} is a required field',\n  defined: '${path} must be defined',\n  notNull: '${path} cannot be null',\n  oneOf: '${path} must be one of the following values: ${values}',\n  notOneOf: '${path} must not be one of the following values: ${values}',\n  notType: ({\n    path,\n    type,\n    value,\n    originalValue\n  }) => {\n    const castMsg = originalValue != null && originalValue !== value ? ` (cast from the value \\`${printValue(originalValue, true)}\\`).` : '.';\n    return type !== 'mixed' ? `${path} must be a \\`${type}\\` type, ` + `but the final value was: \\`${printValue(value, true)}\\`` + castMsg : `${path} must match the configured type. ` + `The validated value was: \\`${printValue(value, true)}\\`` + castMsg;\n  }\n};\nlet string = {\n  length: '${path} must be exactly ${length} characters',\n  min: '${path} must be at least ${min} characters',\n  max: '${path} must be at most ${max} characters',\n  matches: '${path} must match the following: \"${regex}\"',\n  email: '${path} must be a valid email',\n  url: '${path} must be a valid URL',\n  uuid: '${path} must be a valid UUID',\n  datetime: '${path} must be a valid ISO date-time',\n  datetime_precision: '${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits',\n  datetime_offset: '${path} must be a valid ISO date-time with UTC \"Z\" timezone',\n  trim: '${path} must be a trimmed string',\n  lowercase: '${path} must be a lowercase string',\n  uppercase: '${path} must be a upper case string'\n};\nlet number = {\n  min: '${path} must be greater than or equal to ${min}',\n  max: '${path} must be less than or equal to ${max}',\n  lessThan: '${path} must be less than ${less}',\n  moreThan: '${path} must be greater than ${more}',\n  positive: '${path} must be a positive number',\n  negative: '${path} must be a negative number',\n  integer: '${path} must be an integer'\n};\nlet date = {\n  min: '${path} field must be later than ${min}',\n  max: '${path} field must be at earlier than ${max}'\n};\nlet boolean = {\n  isValue: '${path} field must be ${value}'\n};\nlet object = {\n  noUnknown: '${path} field has unspecified keys: ${unknown}',\n  exact: '${path} object contains unknown properties: ${properties}'\n};\nlet array = {\n  min: '${path} field must have at least ${min} items',\n  max: '${path} field must have less than or equal to ${max} items',\n  length: '${path} must have ${length} items'\n};\nlet tuple = {\n  notType: params => {\n    const {\n      path,\n      value,\n      spec\n    } = params;\n    const typeLen = spec.types.length;\n    if (Array.isArray(value)) {\n      if (value.length < typeLen) return `${path} tuple value has too few items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n      if (value.length > typeLen) return `${path} tuple value has too many items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n    }\n    return ValidationError.formatError(mixed.notType, params);\n  }\n};\nvar locale = Object.assign(Object.create(null), {\n  mixed,\n  string,\n  number,\n  date,\n  object,\n  array,\n  boolean,\n  tuple\n});\n\nconst isSchema = obj => obj && obj.__isYupSchema__;\n\nclass Condition {\n  static fromOptions(refs, config) {\n    if (!config.then && !config.otherwise) throw new TypeError('either `then:` or `otherwise:` is required for `when()` conditions');\n    let {\n      is,\n      then,\n      otherwise\n    } = config;\n    let check = typeof is === 'function' ? is : (...values) => values.every(value => value === is);\n    return new Condition(refs, (values, schema) => {\n      var _branch;\n      let branch = check(...values) ? then : otherwise;\n      return (_branch = branch == null ? void 0 : branch(schema)) != null ? _branch : schema;\n    });\n  }\n  constructor(refs, builder) {\n    this.fn = void 0;\n    this.refs = refs;\n    this.refs = refs;\n    this.fn = builder;\n  }\n  resolve(base, options) {\n    let values = this.refs.map(ref =>\n    // TODO: ? operator here?\n    ref.getValue(options == null ? void 0 : options.value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context));\n    let schema = this.fn(values, base, options);\n    if (schema === undefined ||\n    // @ts-ignore this can be base\n    schema === base) {\n      return base;\n    }\n    if (!isSchema(schema)) throw new TypeError('conditions must return a schema object');\n    return schema.resolve(options);\n  }\n}\n\nconst prefixes = {\n  context: '$',\n  value: '.'\n};\nfunction create$9(key, options) {\n  return new Reference(key, options);\n}\nclass Reference {\n  constructor(key, options = {}) {\n    this.key = void 0;\n    this.isContext = void 0;\n    this.isValue = void 0;\n    this.isSibling = void 0;\n    this.path = void 0;\n    this.getter = void 0;\n    this.map = void 0;\n    if (typeof key !== 'string') throw new TypeError('ref must be a string, got: ' + key);\n    this.key = key.trim();\n    if (key === '') throw new TypeError('ref must be a non-empty string');\n    this.isContext = this.key[0] === prefixes.context;\n    this.isValue = this.key[0] === prefixes.value;\n    this.isSibling = !this.isContext && !this.isValue;\n    let prefix = this.isContext ? prefixes.context : this.isValue ? prefixes.value : '';\n    this.path = this.key.slice(prefix.length);\n    this.getter = this.path && (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)(this.path, true);\n    this.map = options.map;\n  }\n  getValue(value, parent, context) {\n    let result = this.isContext ? context : this.isValue ? value : parent;\n    if (this.getter) result = this.getter(result || {});\n    if (this.map) result = this.map(result);\n    return result;\n  }\n\n  /**\n   *\n   * @param {*} value\n   * @param {Object} options\n   * @param {Object=} options.context\n   * @param {Object=} options.parent\n   */\n  cast(value, options) {\n    return this.getValue(value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context);\n  }\n  resolve() {\n    return this;\n  }\n  describe() {\n    return {\n      type: 'ref',\n      key: this.key\n    };\n  }\n  toString() {\n    return `Ref(${this.key})`;\n  }\n  static isRef(value) {\n    return value && value.__isYupRef;\n  }\n}\n\n// @ts-ignore\nReference.prototype.__isYupRef = true;\n\nconst isAbsent = value => value == null;\n\nfunction createValidation(config) {\n  function validate({\n    value,\n    path = '',\n    options,\n    originalValue,\n    schema\n  }, panic, next) {\n    const {\n      name,\n      test,\n      params,\n      message,\n      skipAbsent\n    } = config;\n    let {\n      parent,\n      context,\n      abortEarly = schema.spec.abortEarly,\n      disableStackTrace = schema.spec.disableStackTrace\n    } = options;\n    function resolve(item) {\n      return Reference.isRef(item) ? item.getValue(value, parent, context) : item;\n    }\n    function createError(overrides = {}) {\n      const nextParams = Object.assign({\n        value,\n        originalValue,\n        label: schema.spec.label,\n        path: overrides.path || path,\n        spec: schema.spec,\n        disableStackTrace: overrides.disableStackTrace || disableStackTrace\n      }, params, overrides.params);\n      for (const key of Object.keys(nextParams)) nextParams[key] = resolve(nextParams[key]);\n      const error = new ValidationError(ValidationError.formatError(overrides.message || message, nextParams), value, nextParams.path, overrides.type || name, nextParams.disableStackTrace);\n      error.params = nextParams;\n      return error;\n    }\n    const invalid = abortEarly ? panic : next;\n    let ctx = {\n      path,\n      parent,\n      type: name,\n      from: options.from,\n      createError,\n      resolve,\n      options,\n      originalValue,\n      schema\n    };\n    const handleResult = validOrError => {\n      if (ValidationError.isError(validOrError)) invalid(validOrError);else if (!validOrError) invalid(createError());else next(null);\n    };\n    const handleError = err => {\n      if (ValidationError.isError(err)) invalid(err);else panic(err);\n    };\n    const shouldSkip = skipAbsent && isAbsent(value);\n    if (shouldSkip) {\n      return handleResult(true);\n    }\n    let result;\n    try {\n      var _result;\n      result = test.call(ctx, value, ctx);\n      if (typeof ((_result = result) == null ? void 0 : _result.then) === 'function') {\n        if (options.sync) {\n          throw new Error(`Validation test of type: \"${ctx.type}\" returned a Promise during a synchronous validate. ` + `This test will finish after the validate call has returned`);\n        }\n        return Promise.resolve(result).then(handleResult, handleError);\n      }\n    } catch (err) {\n      handleError(err);\n      return;\n    }\n    handleResult(result);\n  }\n  validate.OPTIONS = config;\n  return validate;\n}\n\nfunction getIn(schema, path, value, context = value) {\n  let parent, lastPart, lastPartDebug;\n\n  // root path: ''\n  if (!path) return {\n    parent,\n    parentPath: path,\n    schema\n  };\n  (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.forEach)(path, (_part, isBracket, isArray) => {\n    let part = isBracket ? _part.slice(1, _part.length - 1) : _part;\n    schema = schema.resolve({\n      context,\n      parent,\n      value\n    });\n    let isTuple = schema.type === 'tuple';\n    let idx = isArray ? parseInt(part, 10) : 0;\n    if (schema.innerType || isTuple) {\n      if (isTuple && !isArray) throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part \"${lastPartDebug}\" must contain an index to the tuple element, e.g. \"${lastPartDebug}[0]\"`);\n      if (value && idx >= value.length) {\n        throw new Error(`Yup.reach cannot resolve an array item at index: ${_part}, in the path: ${path}. ` + `because there is no value at that index. `);\n      }\n      parent = value;\n      value = value && value[idx];\n      schema = isTuple ? schema.spec.types[idx] : schema.innerType;\n    }\n\n    // sometimes the array index part of a path doesn't exist: \"nested.arr.child\"\n    // in these cases the current part is the next schema and should be processed\n    // in this iteration. For cases where the index signature is included this\n    // check will fail and we'll handle the `child` part on the next iteration like normal\n    if (!isArray) {\n      if (!schema.fields || !schema.fields[part]) throw new Error(`The schema does not contain the path: ${path}. ` + `(failed at: ${lastPartDebug} which is a type: \"${schema.type}\")`);\n      parent = value;\n      value = value && value[part];\n      schema = schema.fields[part];\n    }\n    lastPart = part;\n    lastPartDebug = isBracket ? '[' + _part + ']' : '.' + _part;\n  });\n  return {\n    schema,\n    parent,\n    parentPath: lastPart\n  };\n}\nfunction reach(obj, path, value, context) {\n  return getIn(obj, path, value, context).schema;\n}\n\nclass ReferenceSet extends Set {\n  describe() {\n    const description = [];\n    for (const item of this.values()) {\n      description.push(Reference.isRef(item) ? item.describe() : item);\n    }\n    return description;\n  }\n  resolveAll(resolve) {\n    let result = [];\n    for (const item of this.values()) {\n      result.push(resolve(item));\n    }\n    return result;\n  }\n  clone() {\n    return new ReferenceSet(this.values());\n  }\n  merge(newItems, removeItems) {\n    const next = this.clone();\n    newItems.forEach(value => next.add(value));\n    removeItems.forEach(value => next.delete(value));\n    return next;\n  }\n}\n\n// tweaked from https://github.com/Kelin2025/nanoclone/blob/0abeb7635bda9b68ef2277093f76dbe3bf3948e1/src/index.js\nfunction clone(src, seen = new Map()) {\n  if (isSchema(src) || !src || typeof src !== 'object') return src;\n  if (seen.has(src)) return seen.get(src);\n  let copy;\n  if (src instanceof Date) {\n    // Date\n    copy = new Date(src.getTime());\n    seen.set(src, copy);\n  } else if (src instanceof RegExp) {\n    // RegExp\n    copy = new RegExp(src);\n    seen.set(src, copy);\n  } else if (Array.isArray(src)) {\n    // Array\n    copy = new Array(src.length);\n    seen.set(src, copy);\n    for (let i = 0; i < src.length; i++) copy[i] = clone(src[i], seen);\n  } else if (src instanceof Map) {\n    // Map\n    copy = new Map();\n    seen.set(src, copy);\n    for (const [k, v] of src.entries()) copy.set(k, clone(v, seen));\n  } else if (src instanceof Set) {\n    // Set\n    copy = new Set();\n    seen.set(src, copy);\n    for (const v of src) copy.add(clone(v, seen));\n  } else if (src instanceof Object) {\n    // Object\n    copy = {};\n    seen.set(src, copy);\n    for (const [k, v] of Object.entries(src)) copy[k] = clone(v, seen);\n  } else {\n    throw Error(`Unable to clone ${src}`);\n  }\n  return copy;\n}\n\n// If `CustomSchemaMeta` isn't extended with any keys, we'll fall back to a\n// loose Record definition allowing free form usage.\nclass Schema {\n  constructor(options) {\n    this.type = void 0;\n    this.deps = [];\n    this.tests = void 0;\n    this.transforms = void 0;\n    this.conditions = [];\n    this._mutate = void 0;\n    this.internalTests = {};\n    this._whitelist = new ReferenceSet();\n    this._blacklist = new ReferenceSet();\n    this.exclusiveTests = Object.create(null);\n    this._typeCheck = void 0;\n    this.spec = void 0;\n    this.tests = [];\n    this.transforms = [];\n    this.withMutation(() => {\n      this.typeError(mixed.notType);\n    });\n    this.type = options.type;\n    this._typeCheck = options.check;\n    this.spec = Object.assign({\n      strip: false,\n      strict: false,\n      abortEarly: true,\n      recursive: true,\n      disableStackTrace: false,\n      nullable: false,\n      optional: true,\n      coerce: true\n    }, options == null ? void 0 : options.spec);\n    this.withMutation(s => {\n      s.nonNullable();\n    });\n  }\n\n  // TODO: remove\n  get _type() {\n    return this.type;\n  }\n  clone(spec) {\n    if (this._mutate) {\n      if (spec) Object.assign(this.spec, spec);\n      return this;\n    }\n\n    // if the nested value is a schema we can skip cloning, since\n    // they are already immutable\n    const next = Object.create(Object.getPrototypeOf(this));\n\n    // @ts-expect-error this is readonly\n    next.type = this.type;\n    next._typeCheck = this._typeCheck;\n    next._whitelist = this._whitelist.clone();\n    next._blacklist = this._blacklist.clone();\n    next.internalTests = Object.assign({}, this.internalTests);\n    next.exclusiveTests = Object.assign({}, this.exclusiveTests);\n\n    // @ts-expect-error this is readonly\n    next.deps = [...this.deps];\n    next.conditions = [...this.conditions];\n    next.tests = [...this.tests];\n    next.transforms = [...this.transforms];\n    next.spec = clone(Object.assign({}, this.spec, spec));\n    return next;\n  }\n  label(label) {\n    let next = this.clone();\n    next.spec.label = label;\n    return next;\n  }\n  meta(...args) {\n    if (args.length === 0) return this.spec.meta;\n    let next = this.clone();\n    next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n    return next;\n  }\n  withMutation(fn) {\n    let before = this._mutate;\n    this._mutate = true;\n    let result = fn(this);\n    this._mutate = before;\n    return result;\n  }\n  concat(schema) {\n    if (!schema || schema === this) return this;\n    if (schema.type !== this.type && this.type !== 'mixed') throw new TypeError(`You cannot \\`concat()\\` schema's of different types: ${this.type} and ${schema.type}`);\n    let base = this;\n    let combined = schema.clone();\n    const mergedSpec = Object.assign({}, base.spec, combined.spec);\n    combined.spec = mergedSpec;\n    combined.internalTests = Object.assign({}, base.internalTests, combined.internalTests);\n\n    // manually merge the blacklist/whitelist (the other `schema` takes\n    // precedence in case of conflicts)\n    combined._whitelist = base._whitelist.merge(schema._whitelist, schema._blacklist);\n    combined._blacklist = base._blacklist.merge(schema._blacklist, schema._whitelist);\n\n    // start with the current tests\n    combined.tests = base.tests;\n    combined.exclusiveTests = base.exclusiveTests;\n\n    // manually add the new tests to ensure\n    // the deduping logic is consistent\n    combined.withMutation(next => {\n      schema.tests.forEach(fn => {\n        next.test(fn.OPTIONS);\n      });\n    });\n    combined.transforms = [...base.transforms, ...combined.transforms];\n    return combined;\n  }\n  isType(v) {\n    if (v == null) {\n      if (this.spec.nullable && v === null) return true;\n      if (this.spec.optional && v === undefined) return true;\n      return false;\n    }\n    return this._typeCheck(v);\n  }\n  resolve(options) {\n    let schema = this;\n    if (schema.conditions.length) {\n      let conditions = schema.conditions;\n      schema = schema.clone();\n      schema.conditions = [];\n      schema = conditions.reduce((prevSchema, condition) => condition.resolve(prevSchema, options), schema);\n      schema = schema.resolve(options);\n    }\n    return schema;\n  }\n  resolveOptions(options) {\n    var _options$strict, _options$abortEarly, _options$recursive, _options$disableStack;\n    return Object.assign({}, options, {\n      from: options.from || [],\n      strict: (_options$strict = options.strict) != null ? _options$strict : this.spec.strict,\n      abortEarly: (_options$abortEarly = options.abortEarly) != null ? _options$abortEarly : this.spec.abortEarly,\n      recursive: (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive,\n      disableStackTrace: (_options$disableStack = options.disableStackTrace) != null ? _options$disableStack : this.spec.disableStackTrace\n    });\n  }\n\n  /**\n   * Run the configured transform pipeline over an input value.\n   */\n\n  cast(value, options = {}) {\n    let resolvedSchema = this.resolve(Object.assign({\n      value\n    }, options));\n    let allowOptionality = options.assert === 'ignore-optionality';\n    let result = resolvedSchema._cast(value, options);\n    if (options.assert !== false && !resolvedSchema.isType(result)) {\n      if (allowOptionality && isAbsent(result)) {\n        return result;\n      }\n      let formattedValue = printValue(value);\n      let formattedResult = printValue(result);\n      throw new TypeError(`The value of ${options.path || 'field'} could not be cast to a value ` + `that satisfies the schema type: \"${resolvedSchema.type}\". \\n\\n` + `attempted value: ${formattedValue} \\n` + (formattedResult !== formattedValue ? `result of cast: ${formattedResult}` : ''));\n    }\n    return result;\n  }\n  _cast(rawValue, options) {\n    let value = rawValue === undefined ? rawValue : this.transforms.reduce((prevValue, fn) => fn.call(this, prevValue, rawValue, this), rawValue);\n    if (value === undefined) {\n      value = this.getDefault(options);\n    }\n    return value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let {\n      path,\n      originalValue = _value,\n      strict = this.spec.strict\n    } = options;\n    let value = _value;\n    if (!strict) {\n      value = this._cast(value, Object.assign({\n        assert: false\n      }, options));\n    }\n    let initialTests = [];\n    for (let test of Object.values(this.internalTests)) {\n      if (test) initialTests.push(test);\n    }\n    this.runTests({\n      path,\n      value,\n      originalValue,\n      options,\n      tests: initialTests\n    }, panic, initialErrors => {\n      // even if we aren't ending early we can't proceed further if the types aren't correct\n      if (initialErrors.length) {\n        return next(initialErrors, value);\n      }\n      this.runTests({\n        path,\n        value,\n        originalValue,\n        options,\n        tests: this.tests\n      }, panic, next);\n    });\n  }\n\n  /**\n   * Executes a set of validations, either schema, produced Tests or a nested\n   * schema validate result.\n   */\n  runTests(runOptions, panic, next) {\n    let fired = false;\n    let {\n      tests,\n      value,\n      originalValue,\n      path,\n      options\n    } = runOptions;\n    let panicOnce = arg => {\n      if (fired) return;\n      fired = true;\n      panic(arg, value);\n    };\n    let nextOnce = arg => {\n      if (fired) return;\n      fired = true;\n      next(arg, value);\n    };\n    let count = tests.length;\n    let nestedErrors = [];\n    if (!count) return nextOnce([]);\n    let args = {\n      value,\n      originalValue,\n      path,\n      options,\n      schema: this\n    };\n    for (let i = 0; i < tests.length; i++) {\n      const test = tests[i];\n      test(args, panicOnce, function finishTestRun(err) {\n        if (err) {\n          Array.isArray(err) ? nestedErrors.push(...err) : nestedErrors.push(err);\n        }\n        if (--count <= 0) {\n          nextOnce(nestedErrors);\n        }\n      });\n    }\n  }\n  asNestedTest({\n    key,\n    index,\n    parent,\n    parentPath,\n    originalParent,\n    options\n  }) {\n    const k = key != null ? key : index;\n    if (k == null) {\n      throw TypeError('Must include `key` or `index` for nested validations');\n    }\n    const isIndex = typeof k === 'number';\n    let value = parent[k];\n    const testOptions = Object.assign({}, options, {\n      // Nested validations fields are always strict:\n      //    1. parent isn't strict so the casting will also have cast inner values\n      //    2. parent is strict in which case the nested values weren't cast either\n      strict: true,\n      parent,\n      value,\n      originalValue: originalParent[k],\n      // FIXME: tests depend on `index` being passed around deeply,\n      //   we should not let the options.key/index bleed through\n      key: undefined,\n      // index: undefined,\n      [isIndex ? 'index' : 'key']: k,\n      path: isIndex || k.includes('.') ? `${parentPath || ''}[${isIndex ? k : `\"${k}\"`}]` : (parentPath ? `${parentPath}.` : '') + key\n    });\n    return (_, panic, next) => this.resolve(testOptions)._validate(value, testOptions, panic, next);\n  }\n  validate(value, options) {\n    var _options$disableStack2;\n    let schema = this.resolve(Object.assign({}, options, {\n      value\n    }));\n    let disableStackTrace = (_options$disableStack2 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack2 : schema.spec.disableStackTrace;\n    return new Promise((resolve, reject) => schema._validate(value, options, (error, parsed) => {\n      if (ValidationError.isError(error)) error.value = parsed;\n      reject(error);\n    }, (errors, validated) => {\n      if (errors.length) reject(new ValidationError(errors, validated, undefined, undefined, disableStackTrace));else resolve(validated);\n    }));\n  }\n  validateSync(value, options) {\n    var _options$disableStack3;\n    let schema = this.resolve(Object.assign({}, options, {\n      value\n    }));\n    let result;\n    let disableStackTrace = (_options$disableStack3 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack3 : schema.spec.disableStackTrace;\n    schema._validate(value, Object.assign({}, options, {\n      sync: true\n    }), (error, parsed) => {\n      if (ValidationError.isError(error)) error.value = parsed;\n      throw error;\n    }, (errors, validated) => {\n      if (errors.length) throw new ValidationError(errors, value, undefined, undefined, disableStackTrace);\n      result = validated;\n    });\n    return result;\n  }\n  isValid(value, options) {\n    return this.validate(value, options).then(() => true, err => {\n      if (ValidationError.isError(err)) return false;\n      throw err;\n    });\n  }\n  isValidSync(value, options) {\n    try {\n      this.validateSync(value, options);\n      return true;\n    } catch (err) {\n      if (ValidationError.isError(err)) return false;\n      throw err;\n    }\n  }\n  _getDefault(options) {\n    let defaultValue = this.spec.default;\n    if (defaultValue == null) {\n      return defaultValue;\n    }\n    return typeof defaultValue === 'function' ? defaultValue.call(this, options) : clone(defaultValue);\n  }\n  getDefault(options\n  // If schema is defaulted we know it's at least not undefined\n  ) {\n    let schema = this.resolve(options || {});\n    return schema._getDefault(options);\n  }\n  default(def) {\n    if (arguments.length === 0) {\n      return this._getDefault();\n    }\n    let next = this.clone({\n      default: def\n    });\n    return next;\n  }\n  strict(isStrict = true) {\n    return this.clone({\n      strict: isStrict\n    });\n  }\n  nullability(nullable, message) {\n    const next = this.clone({\n      nullable\n    });\n    next.internalTests.nullable = createValidation({\n      message,\n      name: 'nullable',\n      test(value) {\n        return value === null ? this.schema.spec.nullable : true;\n      }\n    });\n    return next;\n  }\n  optionality(optional, message) {\n    const next = this.clone({\n      optional\n    });\n    next.internalTests.optionality = createValidation({\n      message,\n      name: 'optionality',\n      test(value) {\n        return value === undefined ? this.schema.spec.optional : true;\n      }\n    });\n    return next;\n  }\n  optional() {\n    return this.optionality(true);\n  }\n  defined(message = mixed.defined) {\n    return this.optionality(false, message);\n  }\n  nullable() {\n    return this.nullability(true);\n  }\n  nonNullable(message = mixed.notNull) {\n    return this.nullability(false, message);\n  }\n  required(message = mixed.required) {\n    return this.clone().withMutation(next => next.nonNullable(message).defined(message));\n  }\n  notRequired() {\n    return this.clone().withMutation(next => next.nullable().optional());\n  }\n  transform(fn) {\n    let next = this.clone();\n    next.transforms.push(fn);\n    return next;\n  }\n\n  /**\n   * Adds a test function to the schema's queue of tests.\n   * tests can be exclusive or non-exclusive.\n   *\n   * - exclusive tests, will replace any existing tests of the same name.\n   * - non-exclusive: can be stacked\n   *\n   * If a non-exclusive test is added to a schema with an exclusive test of the same name\n   * the exclusive test is removed and further tests of the same name will be stacked.\n   *\n   * If an exclusive test is added to a schema with non-exclusive tests of the same name\n   * the previous tests are removed and further tests of the same name will replace each other.\n   */\n\n  test(...args) {\n    let opts;\n    if (args.length === 1) {\n      if (typeof args[0] === 'function') {\n        opts = {\n          test: args[0]\n        };\n      } else {\n        opts = args[0];\n      }\n    } else if (args.length === 2) {\n      opts = {\n        name: args[0],\n        test: args[1]\n      };\n    } else {\n      opts = {\n        name: args[0],\n        message: args[1],\n        test: args[2]\n      };\n    }\n    if (opts.message === undefined) opts.message = mixed.default;\n    if (typeof opts.test !== 'function') throw new TypeError('`test` is a required parameters');\n    let next = this.clone();\n    let validate = createValidation(opts);\n    let isExclusive = opts.exclusive || opts.name && next.exclusiveTests[opts.name] === true;\n    if (opts.exclusive) {\n      if (!opts.name) throw new TypeError('Exclusive tests must provide a unique `name` identifying the test');\n    }\n    if (opts.name) next.exclusiveTests[opts.name] = !!opts.exclusive;\n    next.tests = next.tests.filter(fn => {\n      if (fn.OPTIONS.name === opts.name) {\n        if (isExclusive) return false;\n        if (fn.OPTIONS.test === validate.OPTIONS.test) return false;\n      }\n      return true;\n    });\n    next.tests.push(validate);\n    return next;\n  }\n  when(keys, options) {\n    if (!Array.isArray(keys) && typeof keys !== 'string') {\n      options = keys;\n      keys = '.';\n    }\n    let next = this.clone();\n    let deps = toArray(keys).map(key => new Reference(key));\n    deps.forEach(dep => {\n      // @ts-ignore readonly array\n      if (dep.isSibling) next.deps.push(dep.key);\n    });\n    next.conditions.push(typeof options === 'function' ? new Condition(deps, options) : Condition.fromOptions(deps, options));\n    return next;\n  }\n  typeError(message) {\n    let next = this.clone();\n    next.internalTests.typeError = createValidation({\n      message,\n      name: 'typeError',\n      skipAbsent: true,\n      test(value) {\n        if (!this.schema._typeCheck(value)) return this.createError({\n          params: {\n            type: this.schema.type\n          }\n        });\n        return true;\n      }\n    });\n    return next;\n  }\n  oneOf(enums, message = mixed.oneOf) {\n    let next = this.clone();\n    enums.forEach(val => {\n      next._whitelist.add(val);\n      next._blacklist.delete(val);\n    });\n    next.internalTests.whiteList = createValidation({\n      message,\n      name: 'oneOf',\n      skipAbsent: true,\n      test(value) {\n        let valids = this.schema._whitelist;\n        let resolved = valids.resolveAll(this.resolve);\n        return resolved.includes(value) ? true : this.createError({\n          params: {\n            values: Array.from(valids).join(', '),\n            resolved\n          }\n        });\n      }\n    });\n    return next;\n  }\n  notOneOf(enums, message = mixed.notOneOf) {\n    let next = this.clone();\n    enums.forEach(val => {\n      next._blacklist.add(val);\n      next._whitelist.delete(val);\n    });\n    next.internalTests.blacklist = createValidation({\n      message,\n      name: 'notOneOf',\n      test(value) {\n        let invalids = this.schema._blacklist;\n        let resolved = invalids.resolveAll(this.resolve);\n        if (resolved.includes(value)) return this.createError({\n          params: {\n            values: Array.from(invalids).join(', '),\n            resolved\n          }\n        });\n        return true;\n      }\n    });\n    return next;\n  }\n  strip(strip = true) {\n    let next = this.clone();\n    next.spec.strip = strip;\n    return next;\n  }\n\n  /**\n   * Return a serialized description of the schema including validations, flags, types etc.\n   *\n   * @param options Provide any needed context for resolving runtime schema alterations (lazy, when conditions, etc).\n   */\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const {\n      label,\n      meta,\n      optional,\n      nullable\n    } = next.spec;\n    const description = {\n      meta,\n      label,\n      optional,\n      nullable,\n      default: next.getDefault(options),\n      type: next.type,\n      oneOf: next._whitelist.describe(),\n      notOneOf: next._blacklist.describe(),\n      tests: next.tests.map(fn => ({\n        name: fn.OPTIONS.name,\n        params: fn.OPTIONS.params\n      })).filter((n, idx, list) => list.findIndex(c => c.name === n.name) === idx)\n    };\n    return description;\n  }\n}\n// @ts-expect-error\nSchema.prototype.__isYupSchema__ = true;\nfor (const method of ['validate', 'validateSync']) Schema.prototype[`${method}At`] = function (path, value, options = {}) {\n  const {\n    parent,\n    parentPath,\n    schema\n  } = getIn(this, path, value, options.context);\n  return schema[method](parent && parent[parentPath], Object.assign({}, options, {\n    parent,\n    path\n  }));\n};\nfor (const alias of ['equals', 'is']) Schema.prototype[alias] = Schema.prototype.oneOf;\nfor (const alias of ['not', 'nope']) Schema.prototype[alias] = Schema.prototype.notOneOf;\n\nconst returnsTrue = () => true;\nfunction create$8(spec) {\n  return new MixedSchema(spec);\n}\nclass MixedSchema extends Schema {\n  constructor(spec) {\n    super(typeof spec === 'function' ? {\n      type: 'mixed',\n      check: spec\n    } : Object.assign({\n      type: 'mixed',\n      check: returnsTrue\n    }, spec));\n  }\n}\ncreate$8.prototype = MixedSchema.prototype;\n\nfunction create$7() {\n  return new BooleanSchema();\n}\nclass BooleanSchema extends Schema {\n  constructor() {\n    super({\n      type: 'boolean',\n      check(v) {\n        if (v instanceof Boolean) v = v.valueOf();\n        return typeof v === 'boolean';\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (ctx.spec.coerce && !ctx.isType(value)) {\n          if (/^(true|1)$/i.test(String(value))) return true;\n          if (/^(false|0)$/i.test(String(value))) return false;\n        }\n        return value;\n      });\n    });\n  }\n  isTrue(message = boolean.isValue) {\n    return this.test({\n      message,\n      name: 'is-value',\n      exclusive: true,\n      params: {\n        value: 'true'\n      },\n      test(value) {\n        return isAbsent(value) || value === true;\n      }\n    });\n  }\n  isFalse(message = boolean.isValue) {\n    return this.test({\n      message,\n      name: 'is-value',\n      exclusive: true,\n      params: {\n        value: 'false'\n      },\n      test(value) {\n        return isAbsent(value) || value === false;\n      }\n    });\n  }\n  default(def) {\n    return super.default(def);\n  }\n  defined(msg) {\n    return super.defined(msg);\n  }\n  optional() {\n    return super.optional();\n  }\n  required(msg) {\n    return super.required(msg);\n  }\n  notRequired() {\n    return super.notRequired();\n  }\n  nullable() {\n    return super.nullable();\n  }\n  nonNullable(msg) {\n    return super.nonNullable(msg);\n  }\n  strip(v) {\n    return super.strip(v);\n  }\n}\ncreate$7.prototype = BooleanSchema.prototype;\n\n/**\n * This file is a modified version of the file from the following repository:\n * Date.parse with progressive enhancement for ISO 8601 <https://github.com/csnover/js-iso8601>\n * NON-CONFORMANT EDITION.\n * © 2011 Colin Snover <http://zetafleet.com>\n * Released under MIT license.\n */\n\n// prettier-ignore\n//                1 YYYY                2 MM        3 DD              4 HH     5 mm        6 ss           7 msec         8 Z 9 ±   10 tzHH    11 tzmm\nconst isoReg = /^(\\d{4}|[+-]\\d{6})(?:-?(\\d{2})(?:-?(\\d{2}))?)?(?:[ T]?(\\d{2}):?(\\d{2})(?::?(\\d{2})(?:[,.](\\d{1,}))?)?(?:(Z)|([+-])(\\d{2})(?::?(\\d{2}))?)?)?$/;\nfunction parseIsoDate(date) {\n  const struct = parseDateStruct(date);\n  if (!struct) return Date.parse ? Date.parse(date) : Number.NaN;\n\n  // timestamps without timezone identifiers should be considered local time\n  if (struct.z === undefined && struct.plusMinus === undefined) {\n    return new Date(struct.year, struct.month, struct.day, struct.hour, struct.minute, struct.second, struct.millisecond).valueOf();\n  }\n  let totalMinutesOffset = 0;\n  if (struct.z !== 'Z' && struct.plusMinus !== undefined) {\n    totalMinutesOffset = struct.hourOffset * 60 + struct.minuteOffset;\n    if (struct.plusMinus === '+') totalMinutesOffset = 0 - totalMinutesOffset;\n  }\n  return Date.UTC(struct.year, struct.month, struct.day, struct.hour, struct.minute + totalMinutesOffset, struct.second, struct.millisecond);\n}\nfunction parseDateStruct(date) {\n  var _regexResult$7$length, _regexResult$;\n  const regexResult = isoReg.exec(date);\n  if (!regexResult) return null;\n\n  // use of toNumber() avoids NaN timestamps caused by “undefined”\n  // values being passed to Date constructor\n  return {\n    year: toNumber(regexResult[1]),\n    month: toNumber(regexResult[2], 1) - 1,\n    day: toNumber(regexResult[3], 1),\n    hour: toNumber(regexResult[4]),\n    minute: toNumber(regexResult[5]),\n    second: toNumber(regexResult[6]),\n    millisecond: regexResult[7] ?\n    // allow arbitrary sub-second precision beyond milliseconds\n    toNumber(regexResult[7].substring(0, 3)) : 0,\n    precision: (_regexResult$7$length = (_regexResult$ = regexResult[7]) == null ? void 0 : _regexResult$.length) != null ? _regexResult$7$length : undefined,\n    z: regexResult[8] || undefined,\n    plusMinus: regexResult[9] || undefined,\n    hourOffset: toNumber(regexResult[10]),\n    minuteOffset: toNumber(regexResult[11])\n  };\n}\nfunction toNumber(str, defaultValue = 0) {\n  return Number(str) || defaultValue;\n}\n\n// Taken from HTML spec: https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address\nlet rEmail =\n// eslint-disable-next-line\n/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\nlet rUrl =\n// eslint-disable-next-line\n/^((https?|ftp):)?\\/\\/(((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:)*@)?(((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]))|((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.?)(:\\d*)?)(\\/((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)+(\\/(([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)*)*)?)?(\\?((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|[\\uE000-\\uF8FF]|\\/|\\?)*)?(\\#((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|\\/|\\?)*)?$/i;\n\n// eslint-disable-next-line\nlet rUUID = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;\nlet yearMonthDay = '^\\\\d{4}-\\\\d{2}-\\\\d{2}';\nlet hourMinuteSecond = '\\\\d{2}:\\\\d{2}:\\\\d{2}';\nlet zOrOffset = '(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)';\nlet rIsoDateTime = new RegExp(`${yearMonthDay}T${hourMinuteSecond}(\\\\.\\\\d+)?${zOrOffset}$`);\nlet isTrimmed = value => isAbsent(value) || value === value.trim();\nlet objStringTag = {}.toString();\nfunction create$6() {\n  return new StringSchema();\n}\nclass StringSchema extends Schema {\n  constructor() {\n    super({\n      type: 'string',\n      check(value) {\n        if (value instanceof String) value = value.valueOf();\n        return typeof value === 'string';\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (!ctx.spec.coerce || ctx.isType(value)) return value;\n\n        // don't ever convert arrays\n        if (Array.isArray(value)) return value;\n        const strValue = value != null && value.toString ? value.toString() : value;\n\n        // no one wants plain objects converted to [Object object]\n        if (strValue === objStringTag) return value;\n        return strValue;\n      });\n    });\n  }\n  required(message) {\n    return super.required(message).withMutation(schema => schema.test({\n      message: message || mixed.required,\n      name: 'required',\n      skipAbsent: true,\n      test: value => !!value.length\n    }));\n  }\n  notRequired() {\n    return super.notRequired().withMutation(schema => {\n      schema.tests = schema.tests.filter(t => t.OPTIONS.name !== 'required');\n      return schema;\n    });\n  }\n  length(length, message = string.length) {\n    return this.test({\n      message,\n      name: 'length',\n      exclusive: true,\n      params: {\n        length\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length === this.resolve(length);\n      }\n    });\n  }\n  min(min, message = string.min) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message = string.max) {\n    return this.test({\n      name: 'max',\n      exclusive: true,\n      message,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length <= this.resolve(max);\n      }\n    });\n  }\n  matches(regex, options) {\n    let excludeEmptyString = false;\n    let message;\n    let name;\n    if (options) {\n      if (typeof options === 'object') {\n        ({\n          excludeEmptyString = false,\n          message,\n          name\n        } = options);\n      } else {\n        message = options;\n      }\n    }\n    return this.test({\n      name: name || 'matches',\n      message: message || string.matches,\n      params: {\n        regex\n      },\n      skipAbsent: true,\n      test: value => value === '' && excludeEmptyString || value.search(regex) !== -1\n    });\n  }\n  email(message = string.email) {\n    return this.matches(rEmail, {\n      name: 'email',\n      message,\n      excludeEmptyString: true\n    });\n  }\n  url(message = string.url) {\n    return this.matches(rUrl, {\n      name: 'url',\n      message,\n      excludeEmptyString: true\n    });\n  }\n  uuid(message = string.uuid) {\n    return this.matches(rUUID, {\n      name: 'uuid',\n      message,\n      excludeEmptyString: false\n    });\n  }\n  datetime(options) {\n    let message = '';\n    let allowOffset;\n    let precision;\n    if (options) {\n      if (typeof options === 'object') {\n        ({\n          message = '',\n          allowOffset = false,\n          precision = undefined\n        } = options);\n      } else {\n        message = options;\n      }\n    }\n    return this.matches(rIsoDateTime, {\n      name: 'datetime',\n      message: message || string.datetime,\n      excludeEmptyString: true\n    }).test({\n      name: 'datetime_offset',\n      message: message || string.datetime_offset,\n      params: {\n        allowOffset\n      },\n      skipAbsent: true,\n      test: value => {\n        if (!value || allowOffset) return true;\n        const struct = parseDateStruct(value);\n        if (!struct) return false;\n        return !!struct.z;\n      }\n    }).test({\n      name: 'datetime_precision',\n      message: message || string.datetime_precision,\n      params: {\n        precision\n      },\n      skipAbsent: true,\n      test: value => {\n        if (!value || precision == undefined) return true;\n        const struct = parseDateStruct(value);\n        if (!struct) return false;\n        return struct.precision === precision;\n      }\n    });\n  }\n\n  //-- transforms --\n  ensure() {\n    return this.default('').transform(val => val === null ? '' : val);\n  }\n  trim(message = string.trim) {\n    return this.transform(val => val != null ? val.trim() : val).test({\n      message,\n      name: 'trim',\n      test: isTrimmed\n    });\n  }\n  lowercase(message = string.lowercase) {\n    return this.transform(value => !isAbsent(value) ? value.toLowerCase() : value).test({\n      message,\n      name: 'string_case',\n      exclusive: true,\n      skipAbsent: true,\n      test: value => isAbsent(value) || value === value.toLowerCase()\n    });\n  }\n  uppercase(message = string.uppercase) {\n    return this.transform(value => !isAbsent(value) ? value.toUpperCase() : value).test({\n      message,\n      name: 'string_case',\n      exclusive: true,\n      skipAbsent: true,\n      test: value => isAbsent(value) || value === value.toUpperCase()\n    });\n  }\n}\ncreate$6.prototype = StringSchema.prototype;\n\n//\n// String Interfaces\n//\n\nlet isNaN$1 = value => value != +value;\nfunction create$5() {\n  return new NumberSchema();\n}\nclass NumberSchema extends Schema {\n  constructor() {\n    super({\n      type: 'number',\n      check(value) {\n        if (value instanceof Number) value = value.valueOf();\n        return typeof value === 'number' && !isNaN$1(value);\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (!ctx.spec.coerce) return value;\n        let parsed = value;\n        if (typeof parsed === 'string') {\n          parsed = parsed.replace(/\\s/g, '');\n          if (parsed === '') return NaN;\n          // don't use parseFloat to avoid positives on alpha-numeric strings\n          parsed = +parsed;\n        }\n\n        // null -> NaN isn't useful; treat all nulls as null and let it fail on\n        // nullability check vs TypeErrors\n        if (ctx.isType(parsed) || parsed === null) return parsed;\n        return parseFloat(parsed);\n      });\n    });\n  }\n  min(min, message = number.min) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message = number.max) {\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value <= this.resolve(max);\n      }\n    });\n  }\n  lessThan(less, message = number.lessThan) {\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        less\n      },\n      skipAbsent: true,\n      test(value) {\n        return value < this.resolve(less);\n      }\n    });\n  }\n  moreThan(more, message = number.moreThan) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        more\n      },\n      skipAbsent: true,\n      test(value) {\n        return value > this.resolve(more);\n      }\n    });\n  }\n  positive(msg = number.positive) {\n    return this.moreThan(0, msg);\n  }\n  negative(msg = number.negative) {\n    return this.lessThan(0, msg);\n  }\n  integer(message = number.integer) {\n    return this.test({\n      name: 'integer',\n      message,\n      skipAbsent: true,\n      test: val => Number.isInteger(val)\n    });\n  }\n  truncate() {\n    return this.transform(value => !isAbsent(value) ? value | 0 : value);\n  }\n  round(method) {\n    var _method;\n    let avail = ['ceil', 'floor', 'round', 'trunc'];\n    method = ((_method = method) == null ? void 0 : _method.toLowerCase()) || 'round';\n\n    // this exists for symemtry with the new Math.trunc\n    if (method === 'trunc') return this.truncate();\n    if (avail.indexOf(method.toLowerCase()) === -1) throw new TypeError('Only valid options for round() are: ' + avail.join(', '));\n    return this.transform(value => !isAbsent(value) ? Math[method](value) : value);\n  }\n}\ncreate$5.prototype = NumberSchema.prototype;\n\n//\n// Number Interfaces\n//\n\nlet invalidDate = new Date('');\nlet isDate = obj => Object.prototype.toString.call(obj) === '[object Date]';\nfunction create$4() {\n  return new DateSchema();\n}\nclass DateSchema extends Schema {\n  constructor() {\n    super({\n      type: 'date',\n      check(v) {\n        return isDate(v) && !isNaN(v.getTime());\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        // null -> InvalidDate isn't useful; treat all nulls as null and let it fail on\n        // nullability check vs TypeErrors\n        if (!ctx.spec.coerce || ctx.isType(value) || value === null) return value;\n        value = parseIsoDate(value);\n\n        // 0 is a valid timestamp equivalent to 1970-01-01T00:00:00Z(unix epoch) or before.\n        return !isNaN(value) ? new Date(value) : DateSchema.INVALID_DATE;\n      });\n    });\n  }\n  prepareParam(ref, name) {\n    let param;\n    if (!Reference.isRef(ref)) {\n      let cast = this.cast(ref);\n      if (!this._typeCheck(cast)) throw new TypeError(`\\`${name}\\` must be a Date or a value that can be \\`cast()\\` to a Date`);\n      param = cast;\n    } else {\n      param = ref;\n    }\n    return param;\n  }\n  min(min, message = date.min) {\n    let limit = this.prepareParam(min, 'min');\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value >= this.resolve(limit);\n      }\n    });\n  }\n  max(max, message = date.max) {\n    let limit = this.prepareParam(max, 'max');\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value <= this.resolve(limit);\n      }\n    });\n  }\n}\nDateSchema.INVALID_DATE = invalidDate;\ncreate$4.prototype = DateSchema.prototype;\ncreate$4.INVALID_DATE = invalidDate;\n\n// @ts-expect-error\nfunction sortFields(fields, excludedEdges = []) {\n  let edges = [];\n  let nodes = new Set();\n  let excludes = new Set(excludedEdges.map(([a, b]) => `${a}-${b}`));\n  function addNode(depPath, key) {\n    let node = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.split)(depPath)[0];\n    nodes.add(node);\n    if (!excludes.has(`${key}-${node}`)) edges.push([key, node]);\n  }\n  for (const key of Object.keys(fields)) {\n    let value = fields[key];\n    nodes.add(key);\n    if (Reference.isRef(value) && value.isSibling) addNode(value.path, key);else if (isSchema(value) && 'deps' in value) value.deps.forEach(path => addNode(path, key));\n  }\n  return toposort__WEBPACK_IMPORTED_MODULE_2___default().array(Array.from(nodes), edges).reverse();\n}\n\nfunction findIndex(arr, err) {\n  let idx = Infinity;\n  arr.some((key, ii) => {\n    var _err$path;\n    if ((_err$path = err.path) != null && _err$path.includes(key)) {\n      idx = ii;\n      return true;\n    }\n  });\n  return idx;\n}\nfunction sortByKeyOrder(keys) {\n  return (a, b) => {\n    return findIndex(keys, a) - findIndex(keys, b);\n  };\n}\n\nconst parseJson = (value, _, ctx) => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  let parsed = value;\n  try {\n    parsed = JSON.parse(value);\n  } catch (err) {\n    /* */\n  }\n  return ctx.isType(parsed) ? parsed : value;\n};\n\n// @ts-ignore\nfunction deepPartial(schema) {\n  if ('fields' in schema) {\n    const partial = {};\n    for (const [key, fieldSchema] of Object.entries(schema.fields)) {\n      partial[key] = deepPartial(fieldSchema);\n    }\n    return schema.setFields(partial);\n  }\n  if (schema.type === 'array') {\n    const nextArray = schema.optional();\n    if (nextArray.innerType) nextArray.innerType = deepPartial(nextArray.innerType);\n    return nextArray;\n  }\n  if (schema.type === 'tuple') {\n    return schema.optional().clone({\n      types: schema.spec.types.map(deepPartial)\n    });\n  }\n  if ('optional' in schema) {\n    return schema.optional();\n  }\n  return schema;\n}\nconst deepHas = (obj, p) => {\n  const path = [...(0,property_expr__WEBPACK_IMPORTED_MODULE_0__.normalizePath)(p)];\n  if (path.length === 1) return path[0] in obj;\n  let last = path.pop();\n  let parent = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)((0,property_expr__WEBPACK_IMPORTED_MODULE_0__.join)(path), true)(obj);\n  return !!(parent && last in parent);\n};\nlet isObject = obj => Object.prototype.toString.call(obj) === '[object Object]';\nfunction unknown(ctx, value) {\n  let known = Object.keys(ctx.fields);\n  return Object.keys(value).filter(key => known.indexOf(key) === -1);\n}\nconst defaultSort = sortByKeyOrder([]);\nfunction create$3(spec) {\n  return new ObjectSchema(spec);\n}\nclass ObjectSchema extends Schema {\n  constructor(spec) {\n    super({\n      type: 'object',\n      check(value) {\n        return isObject(value) || typeof value === 'function';\n      }\n    });\n    this.fields = Object.create(null);\n    this._sortErrors = defaultSort;\n    this._nodes = [];\n    this._excludedEdges = [];\n    this.withMutation(() => {\n      if (spec) {\n        this.shape(spec);\n      }\n    });\n  }\n  _cast(_value, options = {}) {\n    var _options$stripUnknown;\n    let value = super._cast(_value, options);\n\n    //should ignore nulls here\n    if (value === undefined) return this.getDefault(options);\n    if (!this._typeCheck(value)) return value;\n    let fields = this.fields;\n    let strip = (_options$stripUnknown = options.stripUnknown) != null ? _options$stripUnknown : this.spec.noUnknown;\n    let props = [].concat(this._nodes, Object.keys(value).filter(v => !this._nodes.includes(v)));\n    let intermediateValue = {}; // is filled during the transform below\n    let innerOptions = Object.assign({}, options, {\n      parent: intermediateValue,\n      __validating: options.__validating || false\n    });\n    let isChanged = false;\n    for (const prop of props) {\n      let field = fields[prop];\n      let exists = (prop in value);\n      if (field) {\n        let fieldValue;\n        let inputValue = value[prop];\n\n        // safe to mutate since this is fired in sequence\n        innerOptions.path = (options.path ? `${options.path}.` : '') + prop;\n        field = field.resolve({\n          value: inputValue,\n          context: options.context,\n          parent: intermediateValue\n        });\n        let fieldSpec = field instanceof Schema ? field.spec : undefined;\n        let strict = fieldSpec == null ? void 0 : fieldSpec.strict;\n        if (fieldSpec != null && fieldSpec.strip) {\n          isChanged = isChanged || prop in value;\n          continue;\n        }\n        fieldValue = !options.__validating || !strict ?\n        // TODO: use _cast, this is double resolving\n        field.cast(value[prop], innerOptions) : value[prop];\n        if (fieldValue !== undefined) {\n          intermediateValue[prop] = fieldValue;\n        }\n      } else if (exists && !strip) {\n        intermediateValue[prop] = value[prop];\n      }\n      if (exists !== prop in intermediateValue || intermediateValue[prop] !== value[prop]) {\n        isChanged = true;\n      }\n    }\n    return isChanged ? intermediateValue : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let {\n      from = [],\n      originalValue = _value,\n      recursive = this.spec.recursive\n    } = options;\n    options.from = [{\n      schema: this,\n      value: originalValue\n    }, ...from];\n    // this flag is needed for handling `strict` correctly in the context of\n    // validation vs just casting. e.g strict() on a field is only used when validating\n    options.__validating = true;\n    options.originalValue = originalValue;\n    super._validate(_value, options, panic, (objectErrors, value) => {\n      if (!recursive || !isObject(value)) {\n        next(objectErrors, value);\n        return;\n      }\n      originalValue = originalValue || value;\n      let tests = [];\n      for (let key of this._nodes) {\n        let field = this.fields[key];\n        if (!field || Reference.isRef(field)) {\n          continue;\n        }\n        tests.push(field.asNestedTest({\n          options,\n          key,\n          parent: value,\n          parentPath: options.path,\n          originalParent: originalValue\n        }));\n      }\n      this.runTests({\n        tests,\n        value,\n        originalValue,\n        options\n      }, panic, fieldErrors => {\n        next(fieldErrors.sort(this._sortErrors).concat(objectErrors), value);\n      });\n    });\n  }\n  clone(spec) {\n    const next = super.clone(spec);\n    next.fields = Object.assign({}, this.fields);\n    next._nodes = this._nodes;\n    next._excludedEdges = this._excludedEdges;\n    next._sortErrors = this._sortErrors;\n    return next;\n  }\n  concat(schema) {\n    let next = super.concat(schema);\n    let nextFields = next.fields;\n    for (let [field, schemaOrRef] of Object.entries(this.fields)) {\n      const target = nextFields[field];\n      nextFields[field] = target === undefined ? schemaOrRef : target;\n    }\n    return next.withMutation(s =>\n    // XXX: excludes here is wrong\n    s.setFields(nextFields, [...this._excludedEdges, ...schema._excludedEdges]));\n  }\n  _getDefault(options) {\n    if ('default' in this.spec) {\n      return super._getDefault(options);\n    }\n\n    // if there is no default set invent one\n    if (!this._nodes.length) {\n      return undefined;\n    }\n    let dft = {};\n    this._nodes.forEach(key => {\n      var _innerOptions;\n      const field = this.fields[key];\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[key]\n        });\n      }\n      dft[key] = field && 'getDefault' in field ? field.getDefault(innerOptions) : undefined;\n    });\n    return dft;\n  }\n  setFields(shape, excludedEdges) {\n    let next = this.clone();\n    next.fields = shape;\n    next._nodes = sortFields(shape, excludedEdges);\n    next._sortErrors = sortByKeyOrder(Object.keys(shape));\n    // XXX: this carries over edges which may not be what you want\n    if (excludedEdges) next._excludedEdges = excludedEdges;\n    return next;\n  }\n  shape(additions, excludes = []) {\n    return this.clone().withMutation(next => {\n      let edges = next._excludedEdges;\n      if (excludes.length) {\n        if (!Array.isArray(excludes[0])) excludes = [excludes];\n        edges = [...next._excludedEdges, ...excludes];\n      }\n\n      // XXX: excludes here is wrong\n      return next.setFields(Object.assign(next.fields, additions), edges);\n    });\n  }\n  partial() {\n    const partial = {};\n    for (const [key, schema] of Object.entries(this.fields)) {\n      partial[key] = 'optional' in schema && schema.optional instanceof Function ? schema.optional() : schema;\n    }\n    return this.setFields(partial);\n  }\n  deepPartial() {\n    const next = deepPartial(this);\n    return next;\n  }\n  pick(keys) {\n    const picked = {};\n    for (const key of keys) {\n      if (this.fields[key]) picked[key] = this.fields[key];\n    }\n    return this.setFields(picked, this._excludedEdges.filter(([a, b]) => keys.includes(a) && keys.includes(b)));\n  }\n  omit(keys) {\n    const remaining = [];\n    for (const key of Object.keys(this.fields)) {\n      if (keys.includes(key)) continue;\n      remaining.push(key);\n    }\n    return this.pick(remaining);\n  }\n  from(from, to, alias) {\n    let fromGetter = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)(from, true);\n    return this.transform(obj => {\n      if (!obj) return obj;\n      let newObj = obj;\n      if (deepHas(obj, from)) {\n        newObj = Object.assign({}, obj);\n        if (!alias) delete newObj[from];\n        newObj[to] = fromGetter(obj);\n      }\n      return newObj;\n    });\n  }\n\n  /** Parse an input JSON string to an object */\n  json() {\n    return this.transform(parseJson);\n  }\n\n  /**\n   * Similar to `noUnknown` but only validates that an object is the right shape without stripping the unknown keys\n   */\n  exact(message) {\n    return this.test({\n      name: 'exact',\n      exclusive: true,\n      message: message || object.exact,\n      test(value) {\n        if (value == null) return true;\n        const unknownKeys = unknown(this.schema, value);\n        return unknownKeys.length === 0 || this.createError({\n          params: {\n            properties: unknownKeys.join(', ')\n          }\n        });\n      }\n    });\n  }\n  stripUnknown() {\n    return this.clone({\n      noUnknown: true\n    });\n  }\n  noUnknown(noAllow = true, message = object.noUnknown) {\n    if (typeof noAllow !== 'boolean') {\n      message = noAllow;\n      noAllow = true;\n    }\n    let next = this.test({\n      name: 'noUnknown',\n      exclusive: true,\n      message: message,\n      test(value) {\n        if (value == null) return true;\n        const unknownKeys = unknown(this.schema, value);\n        return !noAllow || unknownKeys.length === 0 || this.createError({\n          params: {\n            unknown: unknownKeys.join(', ')\n          }\n        });\n      }\n    });\n    next.spec.noUnknown = noAllow;\n    return next;\n  }\n  unknown(allow = true, message = object.noUnknown) {\n    return this.noUnknown(!allow, message);\n  }\n  transformKeys(fn) {\n    return this.transform(obj => {\n      if (!obj) return obj;\n      const result = {};\n      for (const key of Object.keys(obj)) result[fn(key)] = obj[key];\n      return result;\n    });\n  }\n  camelCase() {\n    return this.transformKeys(tiny_case__WEBPACK_IMPORTED_MODULE_1__.camelCase);\n  }\n  snakeCase() {\n    return this.transformKeys(tiny_case__WEBPACK_IMPORTED_MODULE_1__.snakeCase);\n  }\n  constantCase() {\n    return this.transformKeys(key => (0,tiny_case__WEBPACK_IMPORTED_MODULE_1__.snakeCase)(key).toUpperCase());\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    base.fields = {};\n    for (const [key, value] of Object.entries(next.fields)) {\n      var _innerOptions2;\n      let innerOptions = options;\n      if ((_innerOptions2 = innerOptions) != null && _innerOptions2.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[key]\n        });\n      }\n      base.fields[key] = value.describe(innerOptions);\n    }\n    return base;\n  }\n}\ncreate$3.prototype = ObjectSchema.prototype;\n\nfunction create$2(type) {\n  return new ArraySchema(type);\n}\nclass ArraySchema extends Schema {\n  constructor(type) {\n    super({\n      type: 'array',\n      spec: {\n        types: type\n      },\n      check(v) {\n        return Array.isArray(v);\n      }\n    });\n\n    // `undefined` specifically means uninitialized, as opposed to \"no subtype\"\n    this.innerType = void 0;\n    this.innerType = type;\n  }\n  _cast(_value, _opts) {\n    const value = super._cast(_value, _opts);\n\n    // should ignore nulls here\n    if (!this._typeCheck(value) || !this.innerType) {\n      return value;\n    }\n    let isChanged = false;\n    const castArray = value.map((v, idx) => {\n      const castElement = this.innerType.cast(v, Object.assign({}, _opts, {\n        path: `${_opts.path || ''}[${idx}]`\n      }));\n      if (castElement !== v) {\n        isChanged = true;\n      }\n      return castElement;\n    });\n    return isChanged ? castArray : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    var _options$recursive;\n    // let sync = options.sync;\n    // let path = options.path;\n    let innerType = this.innerType;\n    // let endEarly = options.abortEarly ?? this.spec.abortEarly;\n    let recursive = (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive;\n    options.originalValue != null ? options.originalValue : _value;\n    super._validate(_value, options, panic, (arrayErrors, value) => {\n      var _options$originalValu2;\n      if (!recursive || !innerType || !this._typeCheck(value)) {\n        next(arrayErrors, value);\n        return;\n      }\n\n      // #950 Ensure that sparse array empty slots are validated\n      let tests = new Array(value.length);\n      for (let index = 0; index < value.length; index++) {\n        var _options$originalValu;\n        tests[index] = innerType.asNestedTest({\n          options,\n          index,\n          parent: value,\n          parentPath: options.path,\n          originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n        });\n      }\n      this.runTests({\n        value,\n        tests,\n        originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n        options\n      }, panic, innerTypeErrors => next(innerTypeErrors.concat(arrayErrors), value));\n    });\n  }\n  clone(spec) {\n    const next = super.clone(spec);\n    // @ts-expect-error readonly\n    next.innerType = this.innerType;\n    return next;\n  }\n\n  /** Parse an input JSON string to an object */\n  json() {\n    return this.transform(parseJson);\n  }\n  concat(schema) {\n    let next = super.concat(schema);\n\n    // @ts-expect-error readonly\n    next.innerType = this.innerType;\n    if (schema.innerType)\n      // @ts-expect-error readonly\n      next.innerType = next.innerType ?\n      // @ts-expect-error Lazy doesn't have concat and will break\n      next.innerType.concat(schema.innerType) : schema.innerType;\n    return next;\n  }\n  of(schema) {\n    // FIXME: this should return a new instance of array without the default to be\n    let next = this.clone();\n    if (!isSchema(schema)) throw new TypeError('`array.of()` sub-schema must be a valid yup schema not: ' + printValue(schema));\n\n    // @ts-expect-error readonly\n    next.innerType = schema;\n    next.spec = Object.assign({}, next.spec, {\n      types: schema\n    });\n    return next;\n  }\n  length(length, message = array.length) {\n    return this.test({\n      message,\n      name: 'length',\n      exclusive: true,\n      params: {\n        length\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length === this.resolve(length);\n      }\n    });\n  }\n  min(min, message) {\n    message = message || array.min;\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      // FIXME(ts): Array<typeof T>\n      test(value) {\n        return value.length >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message) {\n    message = message || array.max;\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length <= this.resolve(max);\n      }\n    });\n  }\n  ensure() {\n    return this.default(() => []).transform((val, original) => {\n      // We don't want to return `null` for nullable schema\n      if (this._typeCheck(val)) return val;\n      return original == null ? [] : [].concat(original);\n    });\n  }\n  compact(rejector) {\n    let reject = !rejector ? v => !!v : (v, i, a) => !rejector(v, i, a);\n    return this.transform(values => values != null ? values.filter(reject) : values);\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    if (next.innerType) {\n      var _innerOptions;\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[0]\n        });\n      }\n      base.innerType = next.innerType.describe(innerOptions);\n    }\n    return base;\n  }\n}\ncreate$2.prototype = ArraySchema.prototype;\n\n// @ts-ignore\nfunction create$1(schemas) {\n  return new TupleSchema(schemas);\n}\nclass TupleSchema extends Schema {\n  constructor(schemas) {\n    super({\n      type: 'tuple',\n      spec: {\n        types: schemas\n      },\n      check(v) {\n        const types = this.spec.types;\n        return Array.isArray(v) && v.length === types.length;\n      }\n    });\n    this.withMutation(() => {\n      this.typeError(tuple.notType);\n    });\n  }\n  _cast(inputValue, options) {\n    const {\n      types\n    } = this.spec;\n    const value = super._cast(inputValue, options);\n    if (!this._typeCheck(value)) {\n      return value;\n    }\n    let isChanged = false;\n    const castArray = types.map((type, idx) => {\n      const castElement = type.cast(value[idx], Object.assign({}, options, {\n        path: `${options.path || ''}[${idx}]`\n      }));\n      if (castElement !== value[idx]) isChanged = true;\n      return castElement;\n    });\n    return isChanged ? castArray : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let itemTypes = this.spec.types;\n    super._validate(_value, options, panic, (tupleErrors, value) => {\n      var _options$originalValu2;\n      // intentionally not respecting recursive\n      if (!this._typeCheck(value)) {\n        next(tupleErrors, value);\n        return;\n      }\n      let tests = [];\n      for (let [index, itemSchema] of itemTypes.entries()) {\n        var _options$originalValu;\n        tests[index] = itemSchema.asNestedTest({\n          options,\n          index,\n          parent: value,\n          parentPath: options.path,\n          originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n        });\n      }\n      this.runTests({\n        value,\n        tests,\n        originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n        options\n      }, panic, innerTypeErrors => next(innerTypeErrors.concat(tupleErrors), value));\n    });\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    base.innerType = next.spec.types.map((schema, index) => {\n      var _innerOptions;\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[index]\n        });\n      }\n      return schema.describe(innerOptions);\n    });\n    return base;\n  }\n}\ncreate$1.prototype = TupleSchema.prototype;\n\nfunction create(builder) {\n  return new Lazy(builder);\n}\nfunction catchValidationError(fn) {\n  try {\n    return fn();\n  } catch (err) {\n    if (ValidationError.isError(err)) return Promise.reject(err);\n    throw err;\n  }\n}\nclass Lazy {\n  constructor(builder) {\n    this.type = 'lazy';\n    this.__isYupSchema__ = true;\n    this.spec = void 0;\n    this._resolve = (value, options = {}) => {\n      let schema = this.builder(value, options);\n      if (!isSchema(schema)) throw new TypeError('lazy() functions must return a valid schema');\n      if (this.spec.optional) schema = schema.optional();\n      return schema.resolve(options);\n    };\n    this.builder = builder;\n    this.spec = {\n      meta: undefined,\n      optional: false\n    };\n  }\n  clone(spec) {\n    const next = new Lazy(this.builder);\n    next.spec = Object.assign({}, this.spec, spec);\n    return next;\n  }\n  optionality(optional) {\n    const next = this.clone({\n      optional\n    });\n    return next;\n  }\n  optional() {\n    return this.optionality(true);\n  }\n  resolve(options) {\n    return this._resolve(options.value, options);\n  }\n  cast(value, options) {\n    return this._resolve(value, options).cast(value, options);\n  }\n  asNestedTest(config) {\n    let {\n      key,\n      index,\n      parent,\n      options\n    } = config;\n    let value = parent[index != null ? index : key];\n    return this._resolve(value, Object.assign({}, options, {\n      value,\n      parent\n    })).asNestedTest(config);\n  }\n  validate(value, options) {\n    return catchValidationError(() => this._resolve(value, options).validate(value, options));\n  }\n  validateSync(value, options) {\n    return this._resolve(value, options).validateSync(value, options);\n  }\n  validateAt(path, value, options) {\n    return catchValidationError(() => this._resolve(value, options).validateAt(path, value, options));\n  }\n  validateSyncAt(path, value, options) {\n    return this._resolve(value, options).validateSyncAt(path, value, options);\n  }\n  isValid(value, options) {\n    try {\n      return this._resolve(value, options).isValid(value, options);\n    } catch (err) {\n      if (ValidationError.isError(err)) {\n        return Promise.resolve(false);\n      }\n      throw err;\n    }\n  }\n  isValidSync(value, options) {\n    return this._resolve(value, options).isValidSync(value, options);\n  }\n  describe(options) {\n    return options ? this.resolve(options).describe(options) : {\n      type: 'lazy',\n      meta: this.spec.meta,\n      label: undefined\n    };\n  }\n  meta(...args) {\n    if (args.length === 0) return this.spec.meta;\n    let next = this.clone();\n    next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n    return next;\n  }\n}\n\nfunction setLocale(custom) {\n  Object.keys(custom).forEach(type => {\n    // @ts-ignore\n    Object.keys(custom[type]).forEach(method => {\n      // @ts-ignore\n      locale[type][method] = custom[type][method];\n    });\n  });\n}\n\nfunction addMethod(schemaType, name, fn) {\n  if (!schemaType || !isSchema(schemaType.prototype)) throw new TypeError('You must provide a yup schema constructor function');\n  if (typeof name !== 'string') throw new TypeError('A Method name must be provided');\n  if (typeof fn !== 'function') throw new TypeError('Method function must be provided');\n  schemaType.prototype[name] = fn;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yup/index.esm.js\n");

/***/ })

};
;