{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/span.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/center/absolute-center.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/grid/grid.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/grid/simple-grid.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/icon/icon.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/loader/loader.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/spinner/spinner.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/utils/attr.js", "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js", "(app-pages-browser)/./node_modules/react-icons/fa/index.mjs", "(app-pages-browser)/./node_modules/react-icons/lib/iconBase.mjs", "(app-pages-browser)/./node_modules/react-icons/lib/iconContext.mjs", "(app-pages-browser)/./node_modules/react-icons/lib/iconsManifest.mjs", "(app-pages-browser)/./node_modules/react-icons/lib/index.mjs"]}