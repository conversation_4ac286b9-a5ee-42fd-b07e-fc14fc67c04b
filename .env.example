# ==============================================
# LiftLink Platform Environment Variables
# ==============================================

# Application Environment
NODE_ENV=development
APP_NAME=LiftLink
APP_VERSION=1.0.0

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
DATABASE_URL=postgresql://liftlink_user:liftlink_password@localhost:5432/liftlink
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=liftlink
DATABASE_USER=liftlink_user
DATABASE_PASSWORD=liftlink_password

# ==============================================
# REDIS CONFIGURATION
# ==============================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ==============================================
# JWT CONFIGURATION
# ==============================================
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production-min-32-chars
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# ==============================================
# API CONFIGURATION
# ==============================================
BACKEND_PORT=3001
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
API_PREFIX=/api
API_VERSION=v1

# ==============================================
# SUPABASE CONFIGURATION
# ==============================================
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# ==============================================
# STRIPE PAYMENT CONFIGURATION
# ==============================================
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_CURRENCY=USD

# ==============================================
# GOOGLE SERVICES CONFIGURATION
# ==============================================
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
GOOGLE_OAUTH_CLIENT_ID=your_google_oauth_client_id
GOOGLE_OAUTH_CLIENT_SECRET=your_google_oauth_client_secret

# ==============================================
# FACEBOOK OAUTH CONFIGURATION
# ==============================================
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# ==============================================
# TWILIO SMS CONFIGURATION
# ==============================================
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# ==============================================
# EMAIL CONFIGURATION
# ==============================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM_NAME=LiftLink
EMAIL_FROM_ADDRESS=<EMAIL>

# ==============================================
# FIREBASE PUSH NOTIFICATIONS
# ==============================================
FIREBASE_SERVER_KEY=your_firebase_server_key
FIREBASE_PROJECT_ID=your_firebase_project_id

# ==============================================
# AWS S3 CONFIGURATION (Optional)
# ==============================================
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=liftlink-uploads

# ==============================================
# MAPBOX CONFIGURATION (Alternative to Google Maps)
# ==============================================
MAPBOX_ACCESS_TOKEN=your_mapbox_access_token

# ==============================================
# PAYPAL CONFIGURATION (Alternative Payment)
# ==============================================
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# ==============================================
# SECURITY CONFIGURATION
# ==============================================
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SESSION_SECRET=your-session-secret-key

# ==============================================
# LOGGING CONFIGURATION
# ==============================================
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# ==============================================
# MONITORING & ANALYTICS
# ==============================================
SENTRY_DSN=your_sentry_dsn
GOOGLE_ANALYTICS_ID=your_google_analytics_id

# ==============================================
# DEVELOPMENT TOOLS
# ==============================================
DEBUG=liftlink:*
ENABLE_CORS=true
ENABLE_SWAGGER=true

# ==============================================
# FRONTEND ENVIRONMENT VARIABLES
# ==============================================
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# ==============================================
# MICROSERVICES PORTS
# ==============================================
REALTIME_SERVICE_PORT=3002
PAYMENT_SERVICE_PORT=3003
GEOLOCATION_SERVICE_PORT=3004
NOTIFICATION_SERVICE_PORT=3005
ADMIN_DASHBOARD_PORT=3006

# ==============================================
# BUSINESS CONFIGURATION
# ==============================================
PLATFORM_COMMISSION_RATE=0.20
MINIMUM_FARE=5.00
MAXIMUM_FARE=500.00
SURGE_PRICING_ENABLED=true
DRIVER_BACKGROUND_CHECK_REQUIRED=true

# ==============================================
# FEATURE FLAGS
# ==============================================
ENABLE_RIDE_SCHEDULING=true
ENABLE_MULTI_STOP_RIDES=true
ENABLE_RIDE_SHARING=true
ENABLE_DRIVER_RATINGS=true
ENABLE_PASSENGER_RATINGS=true
ENABLE_EMERGENCY_FEATURES=true
ENABLE_CORPORATE_ACCOUNTS=true

# ==============================================
# TESTING CONFIGURATION
# ==============================================
TEST_DATABASE_URL=postgresql://liftlink_user:liftlink_password@localhost:5432/liftlink_test
TEST_REDIS_URL=redis://localhost:6379/1

# ==============================================
# PRODUCTION OVERRIDES
# ==============================================
# Uncomment and modify for production deployment
# NODE_ENV=production
# DATABASE_URL=*************************************************/liftlink_prod
# REDIS_URL=redis://prod-redis:6379
# FRONTEND_URL=https://liftlink.com
# BACKEND_URL=https://api.liftlink.com
