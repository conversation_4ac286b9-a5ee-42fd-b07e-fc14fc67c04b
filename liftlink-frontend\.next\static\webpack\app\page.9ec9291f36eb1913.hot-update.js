"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/grid/simple-grid.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/icon/icon.js\");\n/* harmony import */ var _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=FaCar,FaComments,FaMapMarkerAlt,FaMoneyBillWave,FaShieldAlt!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Navbar */ \"(app-pages-browser)/./src/app/components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/Footer */ \"(app-pages-browser)/./src/app/components/Footer.tsx\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const textColor = 'gray.700';\n    const { user } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                bg: \"#0080ff\",\n                color: \"white\",\n                py: 20,\n                backgroundImage: \"linear-gradient(135deg, #0080ff 0%, #004d99 100%)\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                    maxW: \"container.xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                        direction: {\n                            base: \"column\",\n                            md: \"row\"\n                        },\n                        align: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                flex: 1,\n                                textAlign: {\n                                    base: \"center\",\n                                    md: \"left\"\n                                },\n                                mb: {\n                                    base: 10,\n                                    md: 0\n                                },\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                alignItems: {\n                                    base: \"center\",\n                                    md: \"flex-start\"\n                                },\n                                gap: 6,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Heading, {\n                                        as: \"h1\",\n                                        size: \"2xl\",\n                                        fontWeight: \"bold\",\n                                        children: \"LiftLink\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                        fontSize: \"xl\",\n                                        maxW: \"600px\",\n                                        children: \"Affordable, Smart Ride Matching for Everyday Commuters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                        fontSize: \"md\",\n                                        maxW: \"600px\",\n                                        children: \"Connect with drivers and passengers headed in the same direction. Save money, reduce traffic, and build community.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                                href: user ? \"/find-ride\" : \"/register\",\n                                                size: \"lg\",\n                                                colorScheme: \"white\",\n                                                variant: \"outline\",\n                                                _hover: {\n                                                    bg: \"whiteAlpha.200\"\n                                                },\n                                                children: user ? \"Find a Ride\" : \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                                href: user ? \"/offer-ride\" : \"/login\",\n                                                size: \"lg\",\n                                                bg: \"white\",\n                                                color: \"#0080ff\",\n                                                _hover: {\n                                                    bg: \"gray.100\"\n                                                },\n                                                children: user ? \"Offer a Ride\" : \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                flex: 1,\n                                display: {\n                                    base: \"none\",\n                                    md: \"block\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                    width: \"500px\",\n                                    height: \"400px\",\n                                    bg: \"whiteAlpha.200\",\n                                    borderRadius: \"lg\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    fontSize: \"6xl\",\n                                    children: \"\\uD83D\\uDE97\\uD83D\\uDCA8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                py: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                    maxW: \"container.xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                        direction: \"column\",\n                        gap: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                direction: \"column\",\n                                gap: 4,\n                                textAlign: \"center\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Heading, {\n                                        as: \"h2\",\n                                        size: \"xl\",\n                                        children: \"Key Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                        fontSize: \"lg\",\n                                        color: textColor,\n                                        maxW: \"800px\",\n                                        children: \"LiftLink makes ride sharing simple, affordable, and safe for everyone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.SimpleGrid, {\n                                columns: {\n                                    base: 1,\n                                    md: 2,\n                                    lg: 3\n                                },\n                                gap: 10,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_12__.FaCar,\n                                        title: \"Ride Posting & Matching\",\n                                        description: \"Post or find rides based on route, time, and price preferences.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_12__.FaComments,\n                                        title: \"Smart Matching\",\n                                        description: \"Our AI automatically matches rides based on similar routes and times.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_12__.FaMoneyBillWave,\n                                        title: \"Flexible Payment\",\n                                        description: \"Pay what you can model within suggested limits.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_12__.FaShieldAlt,\n                                        title: \"Safety & Trust\",\n                                        description: \"Verified profiles, ratings, and real-time trip sharing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_12__.FaMapMarkerAlt,\n                                        title: \"Live Tracking\",\n                                        description: \"Real-time GPS tracking and ETA predictions.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_12__.FaCar,\n                                        title: \"Recurring Rides\",\n                                        description: \"Schedule weekly or daily rides for regular commutes.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"aAjLQdegBonWw6v8amJtA1nd/MY=\", false, function() {\n    return [\n        _store_authStore__WEBPACK_IMPORTED_MODULE_4__.useAuthStore\n    ];\n});\n_c = Home;\nfunction FeatureCard(param) {\n    let { icon, title, description } = param;\n    const bgColor = 'white';\n    const borderColor = 'gray.200';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        p: 6,\n        bg: bgColor,\n        borderRadius: \"lg\",\n        borderWidth: \"1px\",\n        borderColor: borderColor,\n        boxShadow: \"md\",\n        transition: \"transform 0.3s, box-shadow 0.3s\",\n        _hover: {\n            transform: \"translateY(-5px)\",\n            boxShadow: \"lg\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                as: icon,\n                boxSize: 10,\n                color: \"#0080ff\",\n                mb: 4\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Heading, {\n                as: \"h3\",\n                size: \"md\",\n                mb: 2,\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FeatureCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"Home\");\n$RefreshReg$(_c1, \"FeatureCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});