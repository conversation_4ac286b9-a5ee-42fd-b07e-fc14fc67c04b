"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/grid/simple-grid.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/icon/icon.js\");\n/* harmony import */ var _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaCar,FaComments,FaMapMarkerAlt,FaMoneyBillWave,FaShieldAlt!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// import Navbar from \"./components/Navbar\";\n// import Footer from \"./components/Footer\";\nfunction Home() {\n    const textColor = '#4a5568'; // gray.700 equivalent\n    const user = null; // Temporarily disable auth for now\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                bg: \"#0080ff\",\n                color: \"white\",\n                py: 20,\n                backgroundImage: \"linear-gradient(135deg, #0080ff 0%, #004d99 100%)\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Container, {\n                    maxW: \"container.xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                        direction: {\n                            base: \"column\",\n                            md: \"row\"\n                        },\n                        align: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                                flex: 1,\n                                textAlign: {\n                                    base: \"center\",\n                                    md: \"left\"\n                                },\n                                mb: {\n                                    base: 10,\n                                    md: 0\n                                },\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                alignItems: {\n                                    base: \"center\",\n                                    md: \"flex-start\"\n                                },\n                                gap: 6,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                        as: \"h1\",\n                                        size: \"2xl\",\n                                        fontWeight: \"bold\",\n                                        children: \"LiftLink\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        fontSize: \"xl\",\n                                        maxW: \"600px\",\n                                        children: \"Affordable, Smart Ride Matching for Everyday Commuters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        fontSize: \"md\",\n                                        maxW: \"600px\",\n                                        children: \"Connect with drivers and passengers headed in the same direction. Save money, reduce traffic, and build community.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                                href: user ? \"/find-ride\" : \"/register\",\n                                                size: \"lg\",\n                                                colorScheme: \"white\",\n                                                variant: \"outline\",\n                                                _hover: {\n                                                    bg: \"whiteAlpha.200\"\n                                                },\n                                                children: user ? \"Find a Ride\" : \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                                href: user ? \"/offer-ride\" : \"/login\",\n                                                size: \"lg\",\n                                                bg: \"white\",\n                                                color: \"#0080ff\",\n                                                _hover: {\n                                                    bg: \"gray.100\"\n                                                },\n                                                children: user ? \"Offer a Ride\" : \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                                flex: 1,\n                                display: {\n                                    base: \"none\",\n                                    md: \"block\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                                    width: \"500px\",\n                                    height: \"400px\",\n                                    bg: \"whiteAlpha.200\",\n                                    borderRadius: \"lg\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    fontSize: \"6xl\",\n                                    children: \"\\uD83D\\uDE97\\uD83D\\uDCA8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                py: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Container, {\n                    maxW: \"container.xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                        direction: \"column\",\n                        gap: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                                direction: \"column\",\n                                gap: 4,\n                                textAlign: \"center\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                        as: \"h2\",\n                                        size: \"xl\",\n                                        children: \"Key Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        fontSize: \"lg\",\n                                        color: textColor,\n                                        maxW: \"800px\",\n                                        children: \"LiftLink makes ride sharing simple, affordable, and safe for everyone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.SimpleGrid, {\n                                columns: {\n                                    base: 1,\n                                    md: 2,\n                                    lg: 3\n                                },\n                                gap: 10,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaCar,\n                                        title: \"Ride Posting & Matching\",\n                                        description: \"Post or find rides based on route, time, and price preferences.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaComments,\n                                        title: \"Smart Matching\",\n                                        description: \"Our AI automatically matches rides based on similar routes and times.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaMoneyBillWave,\n                                        title: \"Flexible Payment\",\n                                        description: \"Pay what you can model within suggested limits.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaShieldAlt,\n                                        title: \"Safety & Trust\",\n                                        description: \"Verified profiles, ratings, and real-time trip sharing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaMapMarkerAlt,\n                                        title: \"Live Tracking\",\n                                        description: \"Real-time GPS tracking and ETA predictions.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaCar,\n                                        title: \"Recurring Rides\",\n                                        description: \"Schedule weekly or daily rides for regular commutes.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nfunction FeatureCard(param) {\n    let { icon, title, description } = param;\n    const bgColor = 'white';\n    const borderColor = '#e2e8f0'; // gray.200 equivalent\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n        p: 6,\n        bg: bgColor,\n        borderRadius: \"lg\",\n        borderWidth: \"1px\",\n        borderColor: borderColor,\n        boxShadow: \"md\",\n        transition: \"transform 0.3s, box-shadow 0.3s\",\n        _hover: {\n            transform: \"translateY(-5px)\",\n            boxShadow: \"lg\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                as: icon,\n                boxSize: 10,\n                color: \"#0080ff\",\n                mb: 4\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                as: \"h3\",\n                size: \"md\",\n                mb: 2,\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FeatureCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"Home\");\n$RefreshReg$(_c1, \"FeatureCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});