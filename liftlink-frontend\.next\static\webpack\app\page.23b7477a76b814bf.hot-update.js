"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/grid/simple-grid.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/icon/icon.js\");\n/* harmony import */ var _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FaCar,FaComments,FaMapMarkerAlt,FaMoneyBillWave,FaShieldAlt!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Footer */ \"(app-pages-browser)/./src/app/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const textColor = '#4a5568'; // gray.700 equivalent\n    const user = null; // Temporarily disable auth for now\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                bg: \"#0080ff\",\n                color: \"white\",\n                py: 20,\n                backgroundImage: \"linear-gradient(135deg, #0080ff 0%, #004d99 100%)\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Container, {\n                    maxW: \"container.xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Flex, {\n                        direction: {\n                            base: \"column\",\n                            md: \"row\"\n                        },\n                        align: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                flex: 1,\n                                textAlign: {\n                                    base: \"center\",\n                                    md: \"left\"\n                                },\n                                mb: {\n                                    base: 10,\n                                    md: 0\n                                },\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                alignItems: {\n                                    base: \"center\",\n                                    md: \"flex-start\"\n                                },\n                                gap: 6,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                                        as: \"h1\",\n                                        size: \"2xl\",\n                                        fontWeight: \"bold\",\n                                        children: \"LiftLink\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"xl\",\n                                        maxW: \"600px\",\n                                        children: \"Affordable, Smart Ride Matching for Everyday Commuters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"md\",\n                                        maxW: \"600px\",\n                                        children: \"Connect with drivers and passengers headed in the same direction. Save money, reduce traffic, and build community.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Flex, {\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                                href: user ? \"/find-ride\" : \"/register\",\n                                                size: \"lg\",\n                                                colorScheme: \"white\",\n                                                variant: \"outline\",\n                                                _hover: {\n                                                    bg: \"whiteAlpha.200\"\n                                                },\n                                                children: user ? \"Find a Ride\" : \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                                href: user ? \"/offer-ride\" : \"/login\",\n                                                size: \"lg\",\n                                                bg: \"white\",\n                                                color: \"#0080ff\",\n                                                _hover: {\n                                                    bg: \"gray.100\"\n                                                },\n                                                children: user ? \"Offer a Ride\" : \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                flex: 1,\n                                display: {\n                                    base: \"none\",\n                                    md: \"block\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                    width: \"500px\",\n                                    height: \"400px\",\n                                    bg: \"whiteAlpha.200\",\n                                    borderRadius: \"lg\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    fontSize: \"6xl\",\n                                    children: \"\\uD83D\\uDE97\\uD83D\\uDCA8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                py: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Container, {\n                    maxW: \"container.xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Flex, {\n                        direction: \"column\",\n                        gap: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Flex, {\n                                direction: \"column\",\n                                gap: 4,\n                                textAlign: \"center\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                                        as: \"h2\",\n                                        size: \"xl\",\n                                        children: \"Key Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"lg\",\n                                        color: textColor,\n                                        maxW: \"800px\",\n                                        children: \"LiftLink makes ride sharing simple, affordable, and safe for everyone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.SimpleGrid, {\n                                columns: {\n                                    base: 1,\n                                    md: 2,\n                                    lg: 3\n                                },\n                                gap: 10,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaCar,\n                                        title: \"Ride Posting & Matching\",\n                                        description: \"Post or find rides based on route, time, and price preferences.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaComments,\n                                        title: \"Smart Matching\",\n                                        description: \"Our AI automatically matches rides based on similar routes and times.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaMoneyBillWave,\n                                        title: \"Flexible Payment\",\n                                        description: \"Pay what you can model within suggested limits.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaShieldAlt,\n                                        title: \"Safety & Trust\",\n                                        description: \"Verified profiles, ratings, and real-time trip sharing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaMapMarkerAlt,\n                                        title: \"Live Tracking\",\n                                        description: \"Real-time GPS tracking and ETA predictions.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaCar,\n                                        title: \"Recurring Rides\",\n                                        description: \"Schedule weekly or daily rides for regular commutes.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nfunction FeatureCard(param) {\n    let { icon, title, description } = param;\n    const bgColor = 'white';\n    const borderColor = '#e2e8f0'; // gray.200 equivalent\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        p: 6,\n        bg: bgColor,\n        borderRadius: \"lg\",\n        borderWidth: \"1px\",\n        borderColor: borderColor,\n        boxShadow: \"md\",\n        transition: \"transform 0.3s, box-shadow 0.3s\",\n        _hover: {\n            transform: \"translateY(-5px)\",\n            boxShadow: \"lg\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Icon, {\n                as: icon,\n                boxSize: 10,\n                color: \"#0080ff\",\n                mb: 4\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                as: \"h3\",\n                size: \"md\",\n                mb: 2,\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FeatureCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"Home\");\n$RefreshReg$(_c1, \"FeatureCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});