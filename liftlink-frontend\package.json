{"name": "liftlink-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@chakra-ui/react": "^3.18.0", "@chakra-ui/icons": "^2.2.4", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@react-google-maps/api": "^2.20.6", "@supabase/supabase-js": "^2.39.0", "axios": "^1.9.0", "firebase": "^11.7.3", "framer-motion": "^12.11.4", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "yup": "^1.6.1", "zustand": "^5.0.2", "stripe": "^16.12.0", "@stripe/stripe-js": "^4.8.0", "@stripe/react-stripe-js": "^2.8.0", "socket.io-client": "^4.8.1", "date-fns": "^4.1.0", "react-hot-toast": "^2.4.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}