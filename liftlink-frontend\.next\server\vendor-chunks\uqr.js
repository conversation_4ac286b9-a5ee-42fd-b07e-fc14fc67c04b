"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uqr";
exports.ids = ["vendor-chunks/uqr"];
exports.modules = {

/***/ "(ssr)/./node_modules/uqr/dist/index.mjs":
/*!*****************************************!*\
  !*** ./node_modules/uqr/dist/index.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QrCodeDataType: () => (/* binding */ QrCodeDataType),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   renderANSI: () => (/* binding */ renderANSI),\n/* harmony export */   renderSVG: () => (/* binding */ renderSVG),\n/* harmony export */   renderUnicode: () => (/* binding */ renderUnicode),\n/* harmony export */   renderUnicodeCompact: () => (/* binding */ renderUnicodeCompact)\n/* harmony export */ });\nvar QrCodeDataType = /* @__PURE__ */ ((QrCodeDataType2) => {\n  QrCodeDataType2[QrCodeDataType2[\"Border\"] = -1] = \"Border\";\n  QrCodeDataType2[QrCodeDataType2[\"Data\"] = 0] = \"Data\";\n  QrCodeDataType2[QrCodeDataType2[\"Function\"] = 1] = \"Function\";\n  QrCodeDataType2[QrCodeDataType2[\"Position\"] = 2] = \"Position\";\n  QrCodeDataType2[QrCodeDataType2[\"Timing\"] = 3] = \"Timing\";\n  QrCodeDataType2[QrCodeDataType2[\"Alignment\"] = 4] = \"Alignment\";\n  return QrCodeDataType2;\n})(QrCodeDataType || {});\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nconst LOW = [0, 1];\nconst MEDIUM = [1, 0];\nconst QUARTILE = [2, 3];\nconst HIGH = [3, 2];\nconst EccMap = {\n  L: LOW,\n  M: MEDIUM,\n  Q: QUARTILE,\n  H: HIGH\n};\nconst NUMERIC_REGEX = /^[0-9]*$/;\nconst ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\nconst ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\nconst MIN_VERSION = 1;\nconst MAX_VERSION = 40;\nconst PENALTY_N1 = 3;\nconst PENALTY_N2 = 3;\nconst PENALTY_N3 = 40;\nconst PENALTY_N4 = 10;\nconst ECC_CODEWORDS_PER_BLOCK = [\n  // Version: (note that index 0 is for padding, and is set to an illegal value)\n  // 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n  [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n  // Low\n  [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n  // Medium\n  [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n  // Quartile\n  [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]\n  // High\n];\nconst NUM_ERROR_CORRECTION_BLOCKS = [\n  // Version: (note that index 0 is for padding, and is set to an illegal value)\n  // 0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n  [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n  // Low\n  [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n  // Medium\n  [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n  // Quartile\n  [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]\n  // High\n];\nclass QrCode {\n  /* -- Constructor (low level) and fields -- */\n  // Creates a new QR Code with the given version number,\n  // error correction level, data codeword bytes, and mask number.\n  // This is a low-level API that most users should not use directly.\n  // A mid-level API is the encodeSegments() function.\n  constructor(version, ecc, dataCodewords, msk) {\n    this.version = version;\n    this.ecc = ecc;\n    /* -- Fields -- */\n    // The width and height of this QR Code, measured in modules, between\n    // 21 and 177 (inclusive). This is equal to version * 4 + 17.\n    __publicField(this, \"size\");\n    // The index of the mask pattern used in this QR Code, which is between 0 and 7 (inclusive).\n    // Even if a QR Code is created with automatic masking requested (mask = -1),\n    // the resulting object still has a mask value between 0 and 7.\n    __publicField(this, \"mask\");\n    // The modules of this QR Code (false = light, true = dark).\n    // Immutable after constructor finishes. Accessed through getModule().\n    __publicField(this, \"modules\", []);\n    __publicField(this, \"types\", []);\n    if (version < MIN_VERSION || version > MAX_VERSION)\n      throw new RangeError(\"Version value out of range\");\n    if (msk < -1 || msk > 7)\n      throw new RangeError(\"Mask value out of range\");\n    this.size = version * 4 + 17;\n    const row = Array.from({ length: this.size }, () => false);\n    for (let i = 0; i < this.size; i++) {\n      this.modules.push(row.slice());\n      this.types.push(row.map(() => 0));\n    }\n    this.drawFunctionPatterns();\n    const allCodewords = this.addEccAndInterleave(dataCodewords);\n    this.drawCodewords(allCodewords);\n    if (msk === -1) {\n      let minPenalty = 1e9;\n      for (let i = 0; i < 8; i++) {\n        this.applyMask(i);\n        this.drawFormatBits(i);\n        const penalty = this.getPenaltyScore();\n        if (penalty < minPenalty) {\n          msk = i;\n          minPenalty = penalty;\n        }\n        this.applyMask(i);\n      }\n    }\n    this.mask = msk;\n    this.applyMask(msk);\n    this.drawFormatBits(msk);\n  }\n  /* -- Accessor methods -- */\n  // Returns the color of the module (pixel) at the given coordinates, which is false\n  // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n  // If the given coordinates are out of bounds, then false (light) is returned.\n  getModule(x, y) {\n    return x >= 0 && x < this.size && y >= 0 && y < this.size && this.modules[y][x];\n  }\n  /* -- Private helper methods for constructor: Drawing function modules -- */\n  // Reads this object's version field, and draws and marks all function modules.\n  drawFunctionPatterns() {\n    for (let i = 0; i < this.size; i++) {\n      this.setFunctionModule(6, i, i % 2 === 0, QrCodeDataType.Timing);\n      this.setFunctionModule(i, 6, i % 2 === 0, QrCodeDataType.Timing);\n    }\n    this.drawFinderPattern(3, 3);\n    this.drawFinderPattern(this.size - 4, 3);\n    this.drawFinderPattern(3, this.size - 4);\n    const alignPatPos = this.getAlignmentPatternPositions();\n    const numAlign = alignPatPos.length;\n    for (let i = 0; i < numAlign; i++) {\n      for (let j = 0; j < numAlign; j++) {\n        if (!(i === 0 && j === 0 || i === 0 && j === numAlign - 1 || i === numAlign - 1 && j === 0))\n          this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n      }\n    }\n    this.drawFormatBits(0);\n    this.drawVersion();\n  }\n  // Draws two copies of the format bits (with its own error correction code)\n  // based on the given mask and this object's error correction level field.\n  drawFormatBits(mask) {\n    const data = this.ecc[1] << 3 | mask;\n    let rem = data;\n    for (let i = 0; i < 10; i++)\n      rem = rem << 1 ^ (rem >>> 9) * 1335;\n    const bits = (data << 10 | rem) ^ 21522;\n    for (let i = 0; i <= 5; i++)\n      this.setFunctionModule(8, i, getBit(bits, i));\n    this.setFunctionModule(8, 7, getBit(bits, 6));\n    this.setFunctionModule(8, 8, getBit(bits, 7));\n    this.setFunctionModule(7, 8, getBit(bits, 8));\n    for (let i = 9; i < 15; i++)\n      this.setFunctionModule(14 - i, 8, getBit(bits, i));\n    for (let i = 0; i < 8; i++)\n      this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n    for (let i = 8; i < 15; i++)\n      this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n    this.setFunctionModule(8, this.size - 8, true);\n  }\n  // Draws two copies of the version bits (with its own error correction code),\n  // based on this object's version field, iff 7 <= version <= 40.\n  drawVersion() {\n    if (this.version < 7)\n      return;\n    let rem = this.version;\n    for (let i = 0; i < 12; i++)\n      rem = rem << 1 ^ (rem >>> 11) * 7973;\n    const bits = this.version << 12 | rem;\n    for (let i = 0; i < 18; i++) {\n      const color = getBit(bits, i);\n      const a = this.size - 11 + i % 3;\n      const b = Math.floor(i / 3);\n      this.setFunctionModule(a, b, color);\n      this.setFunctionModule(b, a, color);\n    }\n  }\n  // Draws a 9*9 finder pattern including the border separator,\n  // with the center module at (x, y). Modules can be out of bounds.\n  drawFinderPattern(x, y) {\n    for (let dy = -4; dy <= 4; dy++) {\n      for (let dx = -4; dx <= 4; dx++) {\n        const dist = Math.max(Math.abs(dx), Math.abs(dy));\n        const xx = x + dx;\n        const yy = y + dy;\n        if (xx >= 0 && xx < this.size && yy >= 0 && yy < this.size)\n          this.setFunctionModule(xx, yy, dist !== 2 && dist !== 4, QrCodeDataType.Position);\n      }\n    }\n  }\n  // Draws a 5*5 alignment pattern, with the center module\n  // at (x, y). All modules must be in bounds.\n  drawAlignmentPattern(x, y) {\n    for (let dy = -2; dy <= 2; dy++) {\n      for (let dx = -2; dx <= 2; dx++) {\n        this.setFunctionModule(\n          x + dx,\n          y + dy,\n          Math.max(Math.abs(dx), Math.abs(dy)) !== 1,\n          QrCodeDataType.Alignment\n        );\n      }\n    }\n  }\n  // Sets the color of a module and marks it as a function module.\n  // Only used by the constructor. Coordinates must be in bounds.\n  setFunctionModule(x, y, isDark, type = QrCodeDataType.Function) {\n    this.modules[y][x] = isDark;\n    this.types[y][x] = type;\n  }\n  /* -- Private helper methods for constructor: Codewords and masking -- */\n  // Returns a new byte string representing the given data with the appropriate error correction\n  // codewords appended to it, based on this object's version and error correction level.\n  addEccAndInterleave(data) {\n    const ver = this.version;\n    const ecl = this.ecc;\n    if (data.length !== getNumDataCodewords(ver, ecl))\n      throw new RangeError(\"Invalid argument\");\n    const numBlocks = NUM_ERROR_CORRECTION_BLOCKS[ecl[0]][ver];\n    const blockEccLen = ECC_CODEWORDS_PER_BLOCK[ecl[0]][ver];\n    const rawCodewords = Math.floor(getNumRawDataModules(ver) / 8);\n    const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n    const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n    const blocks = [];\n    const rsDiv = reedSolomonComputeDivisor(blockEccLen);\n    for (let i = 0, k = 0; i < numBlocks; i++) {\n      const dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n      k += dat.length;\n      const ecc = reedSolomonComputeRemainder(dat, rsDiv);\n      if (i < numShortBlocks)\n        dat.push(0);\n      blocks.push(dat.concat(ecc));\n    }\n    const result = [];\n    for (let i = 0; i < blocks[0].length; i++) {\n      blocks.forEach((block, j) => {\n        if (i !== shortBlockLen - blockEccLen || j >= numShortBlocks)\n          result.push(block[i]);\n      });\n    }\n    return result;\n  }\n  // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n  // data area of this QR Code. Function modules need to be marked off before this is called.\n  drawCodewords(data) {\n    if (data.length !== Math.floor(getNumRawDataModules(this.version) / 8))\n      throw new RangeError(\"Invalid argument\");\n    let i = 0;\n    for (let right = this.size - 1; right >= 1; right -= 2) {\n      if (right === 6)\n        right = 5;\n      for (let vert = 0; vert < this.size; vert++) {\n        for (let j = 0; j < 2; j++) {\n          const x = right - j;\n          const upward = (right + 1 & 2) === 0;\n          const y = upward ? this.size - 1 - vert : vert;\n          if (!this.types[y][x] && i < data.length * 8) {\n            this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n            i++;\n          }\n        }\n      }\n    }\n  }\n  // XORs the codeword modules in this QR Code with the given mask pattern.\n  // The function modules must be marked and the codeword bits must be drawn\n  // before masking. Due to the arithmetic of XOR, calling applyMask() with\n  // the same mask value a second time will undo the mask. A final well-formed\n  // QR Code needs exactly one (not zero, two, etc.) mask applied.\n  applyMask(mask) {\n    if (mask < 0 || mask > 7)\n      throw new RangeError(\"Mask value out of range\");\n    for (let y = 0; y < this.size; y++) {\n      for (let x = 0; x < this.size; x++) {\n        let invert;\n        switch (mask) {\n          case 0:\n            invert = (x + y) % 2 === 0;\n            break;\n          case 1:\n            invert = y % 2 === 0;\n            break;\n          case 2:\n            invert = x % 3 === 0;\n            break;\n          case 3:\n            invert = (x + y) % 3 === 0;\n            break;\n          case 4:\n            invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 === 0;\n            break;\n          case 5:\n            invert = x * y % 2 + x * y % 3 === 0;\n            break;\n          case 6:\n            invert = (x * y % 2 + x * y % 3) % 2 === 0;\n            break;\n          case 7:\n            invert = ((x + y) % 2 + x * y % 3) % 2 === 0;\n            break;\n          default:\n            throw new Error(\"Unreachable\");\n        }\n        if (!this.types[y][x] && invert)\n          this.modules[y][x] = !this.modules[y][x];\n      }\n    }\n  }\n  // Calculates and returns the penalty score based on state of this QR Code's current modules.\n  // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n  getPenaltyScore() {\n    let result = 0;\n    for (let y = 0; y < this.size; y++) {\n      let runColor = false;\n      let runX = 0;\n      const runHistory = [0, 0, 0, 0, 0, 0, 0];\n      for (let x = 0; x < this.size; x++) {\n        if (this.modules[y][x] === runColor) {\n          runX++;\n          if (runX === 5)\n            result += PENALTY_N1;\n          else if (runX > 5)\n            result++;\n        } else {\n          this.finderPenaltyAddHistory(runX, runHistory);\n          if (!runColor)\n            result += this.finderPenaltyCountPatterns(runHistory) * PENALTY_N3;\n          runColor = this.modules[y][x];\n          runX = 1;\n        }\n      }\n      result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * PENALTY_N3;\n    }\n    for (let x = 0; x < this.size; x++) {\n      let runColor = false;\n      let runY = 0;\n      const runHistory = [0, 0, 0, 0, 0, 0, 0];\n      for (let y = 0; y < this.size; y++) {\n        if (this.modules[y][x] === runColor) {\n          runY++;\n          if (runY === 5)\n            result += PENALTY_N1;\n          else if (runY > 5)\n            result++;\n        } else {\n          this.finderPenaltyAddHistory(runY, runHistory);\n          if (!runColor)\n            result += this.finderPenaltyCountPatterns(runHistory) * PENALTY_N3;\n          runColor = this.modules[y][x];\n          runY = 1;\n        }\n      }\n      result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * PENALTY_N3;\n    }\n    for (let y = 0; y < this.size - 1; y++) {\n      for (let x = 0; x < this.size - 1; x++) {\n        const color = this.modules[y][x];\n        if (color === this.modules[y][x + 1] && color === this.modules[y + 1][x] && color === this.modules[y + 1][x + 1])\n          result += PENALTY_N2;\n      }\n    }\n    let dark = 0;\n    for (const row of this.modules)\n      dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n    const total = this.size * this.size;\n    const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n    result += k * PENALTY_N4;\n    return result;\n  }\n  /* -- Private helper functions -- */\n  // Returns an ascending list of positions of alignment patterns for this version number.\n  // Each position is in the range [0,177), and are used on both the x and y axes.\n  // This could be implemented as lookup table of 40 variable-length lists of integers.\n  getAlignmentPatternPositions() {\n    if (this.version === 1) {\n      return [];\n    } else {\n      const numAlign = Math.floor(this.version / 7) + 2;\n      const step = this.version === 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n      const result = [6];\n      for (let pos = this.size - 7; result.length < numAlign; pos -= step)\n        result.splice(1, 0, pos);\n      return result;\n    }\n  }\n  // Can only be called immediately after a light run is added, and\n  // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n  finderPenaltyCountPatterns(runHistory) {\n    const n = runHistory[1];\n    const core = n > 0 && runHistory[2] === n && runHistory[3] === n * 3 && runHistory[4] === n && runHistory[5] === n;\n    return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n  }\n  // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n  finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n    if (currentRunColor) {\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      currentRunLength = 0;\n    }\n    currentRunLength += this.size;\n    this.finderPenaltyAddHistory(currentRunLength, runHistory);\n    return this.finderPenaltyCountPatterns(runHistory);\n  }\n  // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n  finderPenaltyAddHistory(currentRunLength, runHistory) {\n    if (runHistory[0] === 0)\n      currentRunLength += this.size;\n    runHistory.pop();\n    runHistory.unshift(currentRunLength);\n  }\n}\nfunction appendBits(val, len, bb) {\n  if (len < 0 || len > 31 || val >>> len !== 0)\n    throw new RangeError(\"Value out of range\");\n  for (let i = len - 1; i >= 0; i--)\n    bb.push(val >>> i & 1);\n}\nfunction getBit(x, i) {\n  return (x >>> i & 1) !== 0;\n}\nclass QrSegment {\n  // Creates a new QR Code segment with the given attributes and data.\n  // The character count (numChars) must agree with the mode and the bit buffer length,\n  // but the constraint isn't checked. The given bit buffer is cloned and stored.\n  constructor(mode, numChars, bitData) {\n    this.mode = mode;\n    this.numChars = numChars;\n    this.bitData = bitData;\n    if (numChars < 0)\n      throw new RangeError(\"Invalid argument\");\n    this.bitData = bitData.slice();\n  }\n  /* -- Methods -- */\n  // Returns a new copy of the data bits of this segment.\n  getData() {\n    return this.bitData.slice();\n  }\n}\nconst MODE_NUMERIC = [1, 10, 12, 14];\nconst MODE_ALPHANUMERIC = [2, 9, 11, 13];\nconst MODE_BYTE = [4, 8, 16, 16];\nfunction numCharCountBits(mode, ver) {\n  return mode[Math.floor((ver + 7) / 17) + 1];\n}\nfunction makeBytes(data) {\n  const bb = [];\n  for (const b of data)\n    appendBits(b, 8, bb);\n  return new QrSegment(MODE_BYTE, data.length, bb);\n}\nfunction makeNumeric(digits) {\n  if (!isNumeric(digits))\n    throw new RangeError(\"String contains non-numeric characters\");\n  const bb = [];\n  for (let i = 0; i < digits.length; ) {\n    const n = Math.min(digits.length - i, 3);\n    appendBits(Number.parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n    i += n;\n  }\n  return new QrSegment(MODE_NUMERIC, digits.length, bb);\n}\nfunction makeAlphanumeric(text) {\n  if (!isAlphanumeric(text))\n    throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n  const bb = [];\n  let i;\n  for (i = 0; i + 2 <= text.length; i += 2) {\n    let temp = ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n    temp += ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n    appendBits(temp, 11, bb);\n  }\n  if (i < text.length)\n    appendBits(ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n  return new QrSegment(MODE_ALPHANUMERIC, text.length, bb);\n}\nfunction makeSegments(text) {\n  if (text === \"\")\n    return [];\n  else if (isNumeric(text))\n    return [makeNumeric(text)];\n  else if (isAlphanumeric(text))\n    return [makeAlphanumeric(text)];\n  else\n    return [makeBytes(toUtf8ByteArray(text))];\n}\nfunction isNumeric(text) {\n  return NUMERIC_REGEX.test(text);\n}\nfunction isAlphanumeric(text) {\n  return ALPHANUMERIC_REGEX.test(text);\n}\nfunction getTotalBits(segs, version) {\n  let result = 0;\n  for (const seg of segs) {\n    const ccbits = numCharCountBits(seg.mode, version);\n    if (seg.numChars >= 1 << ccbits)\n      return Number.POSITIVE_INFINITY;\n    result += 4 + ccbits + seg.bitData.length;\n  }\n  return result;\n}\nfunction toUtf8ByteArray(str) {\n  str = encodeURI(str);\n  const result = [];\n  for (let i = 0; i < str.length; i++) {\n    if (str.charAt(i) !== \"%\") {\n      result.push(str.charCodeAt(i));\n    } else {\n      result.push(Number.parseInt(str.substring(i + 1, i + 3), 16));\n      i += 2;\n    }\n  }\n  return result;\n}\nfunction getNumRawDataModules(ver) {\n  if (ver < MIN_VERSION || ver > MAX_VERSION)\n    throw new RangeError(\"Version number out of range\");\n  let result = (16 * ver + 128) * ver + 64;\n  if (ver >= 2) {\n    const numAlign = Math.floor(ver / 7) + 2;\n    result -= (25 * numAlign - 10) * numAlign - 55;\n    if (ver >= 7)\n      result -= 36;\n  }\n  return result;\n}\nfunction getNumDataCodewords(ver, ecl) {\n  return Math.floor(getNumRawDataModules(ver) / 8) - ECC_CODEWORDS_PER_BLOCK[ecl[0]][ver] * NUM_ERROR_CORRECTION_BLOCKS[ecl[0]][ver];\n}\nfunction reedSolomonComputeDivisor(degree) {\n  if (degree < 1 || degree > 255)\n    throw new RangeError(\"Degree out of range\");\n  const result = [];\n  for (let i = 0; i < degree - 1; i++)\n    result.push(0);\n  result.push(1);\n  let root = 1;\n  for (let i = 0; i < degree; i++) {\n    for (let j = 0; j < result.length; j++) {\n      result[j] = reedSolomonMultiply(result[j], root);\n      if (j + 1 < result.length)\n        result[j] ^= result[j + 1];\n    }\n    root = reedSolomonMultiply(root, 2);\n  }\n  return result;\n}\nfunction reedSolomonComputeRemainder(data, divisor) {\n  const result = divisor.map((_) => 0);\n  for (const b of data) {\n    const factor = b ^ result.shift();\n    result.push(0);\n    divisor.forEach((coef, i) => result[i] ^= reedSolomonMultiply(coef, factor));\n  }\n  return result;\n}\nfunction reedSolomonMultiply(x, y) {\n  if (x >>> 8 !== 0 || y >>> 8 !== 0)\n    throw new RangeError(\"Byte out of range\");\n  let z = 0;\n  for (let i = 7; i >= 0; i--) {\n    z = z << 1 ^ (z >>> 7) * 285;\n    z ^= (y >>> i & 1) * x;\n  }\n  return z;\n}\nfunction encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n  if (!(MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= MAX_VERSION) || mask < -1 || mask > 7)\n    throw new RangeError(\"Invalid value\");\n  let version;\n  let dataUsedBits;\n  for (version = minVersion; ; version++) {\n    const dataCapacityBits2 = getNumDataCodewords(version, ecl) * 8;\n    const usedBits = getTotalBits(segs, version);\n    if (usedBits <= dataCapacityBits2) {\n      dataUsedBits = usedBits;\n      break;\n    }\n    if (version >= maxVersion)\n      throw new RangeError(\"Data too long\");\n  }\n  for (const newEcl of [MEDIUM, QUARTILE, HIGH]) {\n    if (boostEcl && dataUsedBits <= getNumDataCodewords(version, newEcl) * 8)\n      ecl = newEcl;\n  }\n  const bb = [];\n  for (const seg of segs) {\n    appendBits(seg.mode[0], 4, bb);\n    appendBits(seg.numChars, numCharCountBits(seg.mode, version), bb);\n    for (const b of seg.getData())\n      bb.push(b);\n  }\n  const dataCapacityBits = getNumDataCodewords(version, ecl) * 8;\n  appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n  appendBits(0, (8 - bb.length % 8) % 8, bb);\n  for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17)\n    appendBits(padByte, 8, bb);\n  const dataCodewords = Array.from({ length: Math.ceil(bb.length / 8) }, () => 0);\n  bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n  return new QrCode(version, ecl, dataCodewords, mask);\n}\n\nfunction encode(data, options) {\n  const {\n    ecc = \"L\",\n    boostEcc = false,\n    minVersion = 1,\n    maxVersion = 40,\n    maskPattern = -1,\n    border = 1\n  } = options || {};\n  const segment = typeof data === \"string\" ? makeSegments(data) : Array.isArray(data) ? [makeBytes(data)] : void 0;\n  if (!segment)\n    throw new Error(`uqr only supports encoding string and binary data, but got: ${typeof data}`);\n  const qr = encodeSegments(\n    segment,\n    EccMap[ecc],\n    minVersion,\n    maxVersion,\n    maskPattern,\n    boostEcc\n  );\n  const result = addBorder({\n    version: qr.version,\n    maskPattern: qr.mask,\n    size: qr.size,\n    data: qr.modules,\n    types: qr.types\n  }, border);\n  if (options?.invert)\n    result.data = result.data.map((row) => row.map((mod) => !mod));\n  options?.onEncoded?.(result);\n  return result;\n}\nfunction addBorder(input, border = 1) {\n  if (!border)\n    return input;\n  const { size } = input;\n  const newSize = size + border * 2;\n  input.size = newSize;\n  input.data.forEach((row) => {\n    for (let i = 0; i < border; i++) {\n      row.unshift(false);\n      row.push(false);\n    }\n  });\n  for (let i = 0; i < border; i++) {\n    input.data.unshift(Array.from({ length: newSize }, (_) => false));\n    input.data.push(Array.from({ length: newSize }, (_) => false));\n  }\n  const b = QrCodeDataType.Border;\n  input.types.forEach((row) => {\n    for (let i = 0; i < border; i++) {\n      row.unshift(b);\n      row.push(b);\n    }\n  });\n  for (let i = 0; i < border; i++) {\n    input.types.unshift(Array.from({ length: newSize }, (_) => b));\n    input.types.push(Array.from({ length: newSize }, (_) => b));\n  }\n  return input;\n}\nfunction getDataAt(data, x, y, defaults = false) {\n  if (x < 0 || y < 0 || x >= data.length || y >= data.length)\n    return defaults;\n  return data[y][x];\n}\n\nfunction renderUnicode(data, options = {}) {\n  const {\n    whiteChar = \"\\u2588\",\n    blackChar = \"\\u2591\"\n  } = options;\n  const result = encode(data, options);\n  return result.data.map((row) => {\n    return row.map((mod) => mod ? blackChar : whiteChar).join(\"\");\n  }).join(\"\\n\");\n}\nfunction renderANSI(data, options = {}) {\n  return renderUnicode(data, {\n    ...options,\n    blackChar: \"\\x1B[40m\\u3000\\x1B[0m\",\n    whiteChar: \"\\x1B[47m\\u3000\\x1B[0m\"\n  });\n}\nfunction renderUnicodeCompact(data, options = {}) {\n  const platte = {\n    WHITE_ALL: \"\\u2588\",\n    WHITE_BLACK: \"\\u2580\",\n    BLACK_WHITE: \"\\u2584\",\n    BLACK_ALL: \" \"\n  };\n  const result = encode(data, options);\n  const WHITE = false;\n  const BLACK = true;\n  const at = (x, y) => getDataAt(result.data, x, y, true);\n  const lines = [];\n  let line = \"\";\n  for (let row = 0; row < result.size; row += 2) {\n    for (let col = 0; col < result.size; col++) {\n      if (at(col, row) === WHITE && at(col, row + 1) === WHITE)\n        line += platte.WHITE_ALL;\n      else if (at(col, row) === WHITE && at(col, row + 1) === BLACK)\n        line += platte.WHITE_BLACK;\n      else if (at(col, row) === BLACK && at(col, row + 1) === WHITE)\n        line += platte.BLACK_WHITE;\n      else\n        line += platte.BLACK_ALL;\n    }\n    lines.push(line);\n    line = \"\";\n  }\n  return lines.join(\"\\n\");\n}\n\nfunction renderSVG(data, options = {}) {\n  const result = encode(data, options);\n  const {\n    pixelSize = 10,\n    whiteColor = \"white\",\n    blackColor = \"black\"\n  } = options;\n  const height = result.size * pixelSize;\n  const width = result.size * pixelSize;\n  let svg = `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 ${width} ${height}\">`;\n  const pathes = [];\n  for (let row = 0; row < result.size; row++) {\n    for (let col = 0; col < result.size; col++) {\n      const x = col * pixelSize;\n      const y = row * pixelSize;\n      if (result.data[row][col])\n        pathes.push(`M${x},${y}h${pixelSize}v${pixelSize}h-${pixelSize}z`);\n    }\n  }\n  svg += `<rect fill=\"${whiteColor}\" width=\"${width}\" height=\"${height}\"/>`;\n  svg += `<path fill=\"${blackColor}\" d=\"${pathes.join(\"\")}\"/>`;\n  svg += \"</svg>\";\n  return svg;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uqr/dist/index.mjs\n");

/***/ })

};
;