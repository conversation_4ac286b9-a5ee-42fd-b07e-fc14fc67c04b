{"c": ["app/layout", "app/register/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/avatar/avatar-context.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/avatar/avatar-fallback.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/avatar/avatar-image.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/avatar/avatar-root-provider.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/avatar/avatar-root.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/avatar/use-avatar-context.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/avatar/use-avatar.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-anchor.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-arrow-tip.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-arrow.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-close-trigger.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-content.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-context.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-description.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-indicator.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-positioner.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-root-provider.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-root.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-title.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/popover-trigger.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/use-popover-context.js", "(app-pages-browser)/./node_modules/@ark-ui/react/dist/components/popover/use-popover.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/avatar/avatar.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/avatar/namespace.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/group/group.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/link/link.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/popover/namespace.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/popover/popover.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/visually-hidden/visually-hidden.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/hooks/use-breakpoint.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/hooks/use-callback-ref.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/hooks/use-disclosure.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/hooks/use-media-query.js", "(app-pages-browser)/./src/app/components/Footer.tsx", "(app-pages-browser)/./src/app/components/Navbar.tsx"]}