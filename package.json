{"name": "liftlink-platform", "version": "1.0.0", "description": "Complete ride-sharing platform with microservices architecture", "private": true, "workspaces": ["liftlink-frontend", "liftlink-backend", "liftlink-admin", "liftlink-mobile", "liftlink-realtime", "liftlink-payments", "liftlink-geolocation", "liftlink-notifications"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:all": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\" \"npm run dev:realtime\" \"npm run dev:admin\"", "dev:frontend": "cd liftlink-frontend && npm run dev", "dev:backend": "cd liftlink-backend && npm run dev", "dev:admin": "cd liftlink-admin && npm run dev", "dev:realtime": "cd liftlink-realtime && npm run dev", "dev:payments": "cd liftlink-payments && npm run dev", "dev:geolocation": "cd liftlink-geolocation && npm run dev", "dev:notifications": "cd liftlink-notifications && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:all": "npm run build:frontend && npm run build:backend && npm run build:admin && npm run build:realtime", "build:frontend": "cd liftlink-frontend && npm run build", "build:backend": "cd liftlink-backend && npm run build", "build:admin": "cd liftlink-admin && npm run build", "build:realtime": "cd liftlink-realtime && npm run build", "start": "npm run start:backend", "start:all": "concurrently \"npm run start:frontend\" \"npm run start:backend\" \"npm run start:realtime\"", "start:frontend": "cd liftlink-frontend && npm start", "start:backend": "cd liftlink-backend && npm start", "start:admin": "cd liftlink-admin && npm start", "start:realtime": "cd liftlink-realtime && npm start", "test": "npm run test:frontend && npm run test:backend", "test:all": "npm run test:frontend && npm run test:backend && npm run test:admin", "test:frontend": "cd liftlink-frontend && npm test", "test:backend": "cd liftlink-backend && npm test", "test:admin": "cd liftlink-admin && npm test", "test:e2e": "cd testing && npm run test:e2e", "lint": "npm run lint:frontend && npm run lint:backend", "lint:all": "npm run lint:frontend && npm run lint:backend && npm run lint:admin", "lint:frontend": "cd liftlink-frontend && npm run lint", "lint:backend": "cd liftlink-backend && npm run lint", "lint:admin": "cd liftlink-admin && npm run lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "setup": "npm install && npm run setup:services", "setup:services": "npm run setup:frontend && npm run setup:backend && npm run setup:admin", "setup:frontend": "cd liftlink-frontend && npm install", "setup:backend": "cd liftlink-backend && npm install", "setup:admin": "cd liftlink-admin && npm install", "setup:realtime": "cd liftlink-realtime && npm install", "setup:payments": "cd liftlink-payments && npm install", "setup:geolocation": "cd liftlink-geolocation && npm install", "setup:notifications": "cd liftlink-notifications && npm install", "db:setup": "cd liftlink-backend && npm run db:generate && npm run db:migrate", "db:migrate": "cd liftlink-backend && npm run db:migrate", "db:seed": "cd liftlink-backend && npm run db:seed", "db:reset": "cd liftlink-backend && npx prisma migrate reset", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "deploy:staging": "npm run build:all && npm run docker:build", "deploy:production": "npm run build:all && npm run docker:build", "clean": "npm run clean:deps && npm run clean:builds", "clean:deps": "rm -rf node_modules */node_modules", "clean:builds": "rm -rf */dist */build */.next", "postinstall": "husky install"}, "devDependencies": {"concurrently": "^8.2.2", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ride-sharing", "transportation", "mobility", "microservices", "nextjs", "nodejs", "typescript", "postgresql", "redis", "stripe", "real-time", "gps-tracking", "payment-processing"], "author": "LiftLink Technologies", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/BhekumusaEric/lift.git"}, "bugs": {"url": "https://github.com/BhekumusaEric/lift/issues"}, "homepage": "https://github.com/BhekumusaEric/lift#readme", "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}