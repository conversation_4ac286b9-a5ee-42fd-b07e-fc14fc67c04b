"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/grid/simple-grid.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/icon/icon.js\");\n/* harmony import */ var _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FaCar,FaComments,FaMapMarkerAlt,FaMoneyBillWave,FaShieldAlt!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Footer */ \"(app-pages-browser)/./src/app/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const textColor = 'gray.700';\n    // const { user } = useAuthStore();\n    const user = null; // Temporarily disable auth\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                bg: \"#0080ff\",\n                color: \"white\",\n                py: 20,\n                backgroundImage: \"linear-gradient(135deg, #0080ff 0%, #004d99 100%)\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Container, {\n                    maxW: \"container.xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Flex, {\n                        direction: {\n                            base: \"column\",\n                            md: \"row\"\n                        },\n                        align: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                flex: 1,\n                                textAlign: {\n                                    base: \"center\",\n                                    md: \"left\"\n                                },\n                                mb: {\n                                    base: 10,\n                                    md: 0\n                                },\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                alignItems: {\n                                    base: \"center\",\n                                    md: \"flex-start\"\n                                },\n                                gap: 6,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                                        as: \"h1\",\n                                        size: \"2xl\",\n                                        fontWeight: \"bold\",\n                                        children: \"LiftLink\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"xl\",\n                                        maxW: \"600px\",\n                                        children: \"Affordable, Smart Ride Matching for Everyday Commuters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"md\",\n                                        maxW: \"600px\",\n                                        children: \"Connect with drivers and passengers headed in the same direction. Save money, reduce traffic, and build community.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Flex, {\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                                href: user ? \"/find-ride\" : \"/register\",\n                                                size: \"lg\",\n                                                colorScheme: \"white\",\n                                                variant: \"outline\",\n                                                _hover: {\n                                                    bg: \"whiteAlpha.200\"\n                                                },\n                                                children: user ? \"Find a Ride\" : \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                                href: user ? \"/offer-ride\" : \"/login\",\n                                                size: \"lg\",\n                                                bg: \"white\",\n                                                color: \"#0080ff\",\n                                                _hover: {\n                                                    bg: \"gray.100\"\n                                                },\n                                                children: user ? \"Offer a Ride\" : \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                flex: 1,\n                                display: {\n                                    base: \"none\",\n                                    md: \"block\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                    width: \"500px\",\n                                    height: \"400px\",\n                                    bg: \"whiteAlpha.200\",\n                                    borderRadius: \"lg\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    fontSize: \"6xl\",\n                                    children: \"\\uD83D\\uDE97\\uD83D\\uDCA8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                py: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Container, {\n                    maxW: \"container.xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Flex, {\n                        direction: \"column\",\n                        gap: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Flex, {\n                                direction: \"column\",\n                                gap: 4,\n                                textAlign: \"center\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                                        as: \"h2\",\n                                        size: \"xl\",\n                                        children: \"Key Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"lg\",\n                                        color: textColor,\n                                        maxW: \"800px\",\n                                        children: \"LiftLink makes ride sharing simple, affordable, and safe for everyone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.SimpleGrid, {\n                                columns: {\n                                    base: 1,\n                                    md: 2,\n                                    lg: 3\n                                },\n                                gap: 10,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaCar,\n                                        title: \"Ride Posting & Matching\",\n                                        description: \"Post or find rides based on route, time, and price preferences.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaComments,\n                                        title: \"Smart Matching\",\n                                        description: \"Our AI automatically matches rides based on similar routes and times.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaMoneyBillWave,\n                                        title: \"Flexible Payment\",\n                                        description: \"Pay what you can model within suggested limits.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaShieldAlt,\n                                        title: \"Safety & Trust\",\n                                        description: \"Verified profiles, ratings, and real-time trip sharing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaMapMarkerAlt,\n                                        title: \"Live Tracking\",\n                                        description: \"Real-time GPS tracking and ETA predictions.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                        icon: _barrel_optimize_names_FaCar_FaComments_FaMapMarkerAlt_FaMoneyBillWave_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaCar,\n                                        title: \"Recurring Rides\",\n                                        description: \"Schedule weekly or daily rides for regular commutes.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nfunction FeatureCard(param) {\n    let { icon, title, description } = param;\n    const bgColor = 'white';\n    const borderColor = 'gray.200';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        p: 6,\n        bg: bgColor,\n        borderRadius: \"lg\",\n        borderWidth: \"1px\",\n        borderColor: borderColor,\n        boxShadow: \"md\",\n        transition: \"transform 0.3s, box-shadow 0.3s\",\n        _hover: {\n            transform: \"translateY(-5px)\",\n            boxShadow: \"lg\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Icon, {\n                as: icon,\n                boxSize: 10,\n                color: \"#0080ff\",\n                mb: 4\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                as: \"h3\",\n                size: \"md\",\n                mb: 2,\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\lift\\\\liftlink-frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FeatureCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"Home\");\n$RefreshReg$(_c1, \"FeatureCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});