{"version": "2.0.0", "tasks": [{"label": "Start LiftLink Dev Server", "type": "npm", "script": "dev", "path": "liftlink-frontend/", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "runOptions": {"runOn": "default"}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "npm", "script": "install", "path": "liftlink-frontend/", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}, {"label": "Build Production", "type": "npm", "script": "build", "path": "liftlink-frontend/", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}]}