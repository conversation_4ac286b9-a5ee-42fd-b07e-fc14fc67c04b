{"name": "liftlink-backend", "version": "1.0.0", "description": "LiftLink Backend API - Node.js Express TypeScript", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "npx prisma db seed", "docker:build": "docker build -t liftlink-backend .", "docker:run": "docker run -p 3001:3001 liftlink-backend"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "passport-facebook": "^3.0.0", "@supabase/supabase-js": "^2.38.4", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "redis": "^4.6.11", "ioredis": "^5.3.2", "socket.io": "^4.7.4", "stripe": "^14.9.0", "twilio": "^4.19.0", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "axios": "^1.6.2", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "moment": "^2.29.4", "uuid": "^9.0.1", "geolib": "^3.3.4", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-facebook": "^3.0.3", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ride-sharing", "transportation", "api", "nodejs", "typescript", "express", "postgresql", "redis", "stripe", "real-time"], "author": "LiftLink Technologies", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/BhekumusaEric/lift.git"}, "bugs": {"url": "https://github.com/BhekumusaEric/lift/issues"}, "homepage": "https://github.com/BhekumusaEric/lift#readme"}