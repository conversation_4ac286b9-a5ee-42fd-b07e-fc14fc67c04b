{"version": "0.2.0", "configurations": [{"name": "Launch LiftLink", "type": "node", "request": "launch", "program": "${workspaceFolder}/liftlink-frontend/node_modules/.bin/next", "args": ["dev"], "cwd": "${workspaceFolder}/liftlink-frontend", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**"]}, {"name": "Debug LiftLink", "type": "node", "request": "launch", "program": "${workspaceFolder}/liftlink-frontend/node_modules/.bin/next", "args": ["dev"], "cwd": "${workspaceFolder}/liftlink-frontend", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**"], "port": 9229}]}